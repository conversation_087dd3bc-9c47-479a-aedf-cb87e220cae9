import { Paper, Typography, CircularProgress } from "@mui/material";
import {
	EWalletExternalPaymentStatus,
	getGetEwalletQueryKey,
	useGetEwalletExternalPayment,
} from "@/api/ewallet";
import { getErrorText } from "@/helpers/errors.ts";
import { includes } from "@/helpers/array.ts";
import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import EwalletPaymentContent from "./EwalletPaymentContent";

interface EwalletPaymentProps {
	paymentId: string;
}

export default function EwalletPayment({ paymentId }: EwalletPaymentProps) {
	const payment = useGetEwalletExternalPayment(paymentId, {
		query: {
			refetchInterval: query => {
				if (
					!includes(
						query.state.data?.status,
						EWalletExternalPaymentStatus.CANCELLED,
						EWalletExternalPaymentStatus.REJECTED,
						EWalletExternalPaymentStatus.SUCCESS
					)
				) {
					return 5_000;
				}
				return false;
			},
		},
	});

	const queryClient = useQueryClient();
	useEffect(() => {
		if (
			includes(
				payment.data?.status,
				EWalletExternalPaymentStatus.CANCELLED,
				EWalletExternalPaymentStatus.REJECTED,
				EWalletExternalPaymentStatus.SUCCESS
			)
		) {
			queryClient.refetchQueries({
				queryKey: getGetEwalletQueryKey(payment.data?.ewallet.uuid_id),
				exact: false,
			});
		}
	});

	if (payment.isPending) {
        return (
            <Paper elevation={3} sx={{ borderRadius: 5, overflow: "hidden", p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                <CircularProgress />
                <Typography>Очікуємо на підтвердження оплати...</Typography>
            </Paper>
        );
    }

	return (
		<Paper elevation={3} sx={{ borderRadius: 5, overflow: "hidden" }}>
			{payment.isError ? (
				<Typography sx={{ fontWeight: 500, color: "error.main", p: 3 }}>
					{getErrorText(payment.error)}
				</Typography>
			) : (
				<EwalletPaymentContent payment={payment.data} />
			)}
		</Paper>
	);
}