import { Box, Modal, CircularProgress} from "@mui/material";
import {
	EWalletExternalPaymentSchema,
	getGetEwalletExternalPaymentQueryKey,
	getGetEwalletExternalPaymentQueryOptions,
} from "@/api/ewallet";
import PaymentInfoOverlay from "./PaymentInfoOverlay";
import AdVideo from "@/features/AdVideo.tsx";
import { useQueryClient } from "@tanstack/react-query";
import { includes } from "@/helpers/array.ts";
import { getGetNextAdQueryKey } from "@/api/ad";

export interface AdPendingScreenProps {
	payment: EWalletExternalPaymentSchema;
	showAd: boolean;
}

export default function AdPendingScreen({ payment, showAd }: AdPendingScreenProps) {
	const queryClient = useQueryClient();

	// If no ad is configured, show a default pending screen
	if (!payment.ewallet.ad_id) {
		return (
			<Modal open={showAd}>
				<Box sx={{
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
					justifyContent: 'center',
					height: '100%',
					color: 'white',
					bgcolor: 'rgba(0, 0, 0, 0.8)',
					gap: 2
				}}>
					<CircularProgress color="inherit" />
					{/*<Typography>Очікуємо на підтвердження оплати...</Typography>*/}
					<PaymentInfoOverlay payment={payment} />
				</Box>
			</Modal>
		);
	}

	// If an ad is configured, show it
	return (
		<Modal open={showAd}>
			<Box
				sx={{
					position: "relative",
					width: "100%",
					height: "100%",
					bgcolor: "#000",
				}}
			>
				<AdVideo
					adId={payment.ewallet.ad_id}
					onEnded={event => {
						event.preventDefault();

						queryClient
							.invalidateQueries({
								queryKey: getGetEwalletExternalPaymentQueryKey(payment.uuid_id),
								exact: false,
							})
							.then(() =>
								queryClient
									.ensureQueryData(
										getGetEwalletExternalPaymentQueryOptions(payment.uuid_id)
									)
									.then(payment => {
										if (includes(payment.status, "CREATED", "PENDING")) {
											queryClient.refetchQueries({
												queryKey: getGetNextAdQueryKey(
													payment.ewallet.ad_id
												),
												exact: false,
											});
										}
									})
							);
					}}
				/>
				<PaymentInfoOverlay payment={payment} />
			</Box>
		</Modal>
	);
}
