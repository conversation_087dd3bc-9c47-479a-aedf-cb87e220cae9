import useAuth from "@/auth/useAuth.ts";
import { PropsWithEWallet } from "../types.ts";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { FormValidateOrFn } from "@tanstack/form-core";
import { NotAuthorizedBlock } from "./NotAuthorizedBlock.tsx";
import { useCreateEwalletExternalPayment } from "@/api/ewallet";
import { useProcessCheck } from "@/api/client";
import { ewalletPayFormOpts, useEwalletPayForm } from "./form.ts";
import useAppContextPart from "@/AppContext/useAppContextPart.ts";
import EWalletPayFormSchema, { EWalletPayFormType } from "./schema";
import { NotPositiveAmountBlock } from "./NotPositiveAmountBlock.tsx";
import { AmountBlock, SubmitBlock, TransferDataBlock } from "./form-blocks";
import { useTrackEvent } from "@/AppAnalytics/hooks/useTrackEvents.tsx";
import { AnalyticsActions } from "@/AppAnalytics/analytics.interface.ts";
import { LoyaltyBlock } from "@/features/loyalty";
import { useLoyaltyDetection } from "@/hooks/loyalty";
import useGroup from "@/hooks/useGroup";
import { useEffect, useState } from "react";
import { IncustTerminalApiClientModelsCheckCheck } from "@/api/client/schemas";
import { detectTransferType } from "@/routes/$lang/_shop/-components/ewallet/EwalletPay/helpers";

function ReactiveEwalletLogic({
	ewallet,
	amount,
	transferDataRaw,
}: {
	ewallet: PropsWithEWallet["ewallet"];
	amount: number | undefined;
	transferDataRaw: { data: string } | undefined;
}) {
	const { isAuthorised } = useAuth();
	const group = useGroup();
	const showError = useAppContextPart("showError");
	const { brand_id } = useSearch({
		from: "/$lang/_shop/_no_store/ewallet/$ewalletId/pay",
	});

	const [processedCheck, setProcessedCheck] = useState<IncustTerminalApiClientModelsCheckCheck | null>(null);

	const loyaltyDetection = useLoyaltyDetection({
		ewallet_id: ewallet.id,
		transfer_data: transferDataRaw?.data,
		profile_id: group?.id,
		is_ewallet_external_payment: true,
		enabled: isAuthorised && Boolean(ewallet.id) && !!transferDataRaw?.data,
	});

	const processCheckMutation = useProcessCheck({
		mutation: {
			onSuccess: (data: IncustTerminalApiClientModelsCheckCheck) => {
				setProcessedCheck(data);
			},
			onError: (error) => {
				console.error("Process check failed", error);
				showError(error.message);
				setProcessedCheck(null);
			}
		}
	});

	const [debouncedAmount, setDebouncedAmount] = useState(amount);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedAmount(amount);
		}, 500);

		return () => {
			clearTimeout(handler);
		};
	}, [amount]);

	useEffect(() => {
		const canProcess = 
			debouncedAmount && debouncedAmount > 0 && 
			loyaltyDetection.hasLoyaltySettings && 
			loyaltyDetection.productCode &&
			loyaltyDetection.loyaltySettingsId &&
			brand_id;

		if (!canProcess) {
			setProcessedCheck(null);
			return;
		}

		processCheckMutation.mutate({
			brandId: brand_id,
			data: {
				loyalty_settings_id: loyaltyDetection.loyaltySettingsId,
				amount: debouncedAmount,
				check_items: [
					{
						code: loyaltyDetection.productCode!,
						price: debouncedAmount,
						quantity: 1,
					},
				],
				customer_id: group?.id?.toString(),
			},
		});
	}, [debouncedAmount, loyaltyDetection.hasLoyaltySettings, loyaltyDetection.productCode, loyaltyDetection.loyaltySettingsId, group?.id, processCheckMutation.mutate, brand_id]);

	return (
		<LoyaltyBlock
			loyaltyQuery={loyaltyDetection}
			showMerchant
			processedCheck={processedCheck}
			isProcessingCheck={processCheckMutation.isPending}
			processCheckError={processCheckMutation.error as Error | null}
			currency={ewallet.currency}
		/>
	);
}

export default function EWalletPayForm({ ewallet }: PropsWithEWallet) {
	const { isAuthorised } = useAuth();
	const trackEvent = useTrackEvent();

	const createEwalletExternalPayment = useCreateEwalletExternalPayment({
		mutation: {
			onSuccess: async payment => {
				await navigate({
					to: "/$lang/ewallet/payment/$paymentId",
					params: prev => ({ ...prev, paymentId: payment.uuid_id }),
					search: true,
					replace: true,
				});
			},
			onError: error => {
				console.error(error);
				showError(error);
			},
		},
	});

	const navigate = useNavigate({
		from: "/$lang",
	});

	const form = useEwalletPayForm({
		...ewalletPayFormOpts,
		validators: {
			onSubmit: EWalletPayFormSchema as FormValidateOrFn<EWalletPayFormType>,
		} as (typeof ewalletPayFormOpts)["validators"],
		onSubmit: async ({ value }) => {
			const parsed = EWalletPayFormSchema.parse(value);

			console.log("submit", value);
			await createEwalletExternalPayment.mutateAsync({
				data: {
					ewallet_id: ewallet.id,
					amount: parsed.amount,
					transfer_type: detectTransferType(parsed.transfer_data.data)!,
					transfer_data: parsed.transfer_data,
				},
			});

			trackEvent(AnalyticsActions.INITIATE_CHECKOUT, {
				category: "initiate_ewallet_checkout",
				event_id: ewallet?.id + "initiate",
				properties: {
					ewallet_id: ewallet.id,
					amount: parsed.amount,
					transfer_type: detectTransferType(parsed.transfer_data.data)!,
					transfer_data: parsed.transfer_data,
				},
			});
		},
		onSubmitInvalid: ({ value }) => {
			console.log("submit invalid", value, form.state.errors);
		},
	});

	const showError = useAppContextPart("showError");

	return (
		<form
			noValidate
			onSubmit={e => {
				e.preventDefault();
				e.stopPropagation();
				form.handleSubmit();
			}}
		>
			{!isAuthorised && <NotAuthorizedBlock />}

			{isAuthorised && (
				<>
					<NotPositiveAmountBlock ewallet={ewallet} />

					{ewallet?.account_info?.is_user_special_account && (
						<TransferDataBlock form={form} ewallet={ewallet} />
					)}

					<AmountBlock form={form} ewallet={ewallet} />

					<form.Subscribe
						selector={(state) => [state.values.amount, state.values.transfer_data] as const}
					>
						{([amount, transferData]) => (
							<ReactiveEwalletLogic 
								ewallet={ewallet} 
								amount={amount} 
								transferDataRaw={transferData} 
							/>
						)}
					</form.Subscribe>
				</>
			)}

			<SubmitBlock form={form} ewallet={ewallet} />
		</form>
	);
}
