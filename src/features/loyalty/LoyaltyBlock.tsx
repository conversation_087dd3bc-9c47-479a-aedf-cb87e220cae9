import { Box, Typography, Chip, Alert, CircularProgress } from "@mui/material";
import { DiscountOutlined as DiscountIcon } from "@mui/icons-material";
import type { UseLoyaltyDetectionResult } from "@/hooks/loyalty/useLoyaltyDetection";
import formatCurrency from "@/helpers/formatCurrency.ts";
import { IncustTerminalApiClientModelsCheckCheck } from "@/api/client/schemas";

export interface LoyaltyBlockProps {
	/** Result from useLoyaltyDetection hook */
	loyaltyQuery: UseLoyaltyDetectionResult;
	/** Result from processCheck mutation */
	processedCheck?: IncustTerminalApiClientModelsCheckCheck | null;
	/** Loading state of processCheck mutation */
	isProcessingCheck?: boolean;
	/** Error state of processCheck mutation */
	processCheckError?: Error | null;
	/** Currency for formatting amounts */
	currency?: string;
	/** Show merchant info if found */
	showMerchant?: boolean;
	/** Show loading state */
	showLoading?: boolean;
	/** Custom className */
	className?: string;
}

/**
 * Universal component for displaying loyalty UI elements
 */
export const LoyaltyBlock = ({
	loyaltyQuery,
	processedCheck,
	isProcessingCheck,
	processCheckError,
	currency,
	showMerchant = true,
	showLoading = true,
	className,
}: LoyaltyBlockProps) => {
	if (!loyaltyQuery.isLoading && !loyaltyQuery.data && !loyaltyQuery.error) {
		return null;
	}

	if (loyaltyQuery.isLoading && showLoading) {
		return (
			<Box className={className} sx={{ p: 2 }}>
				<Typography variant="body2" color="text.secondary">
					Завантаження інформації про лояльність...
				</Typography>
			</Box>
		);
	}

	if (loyaltyQuery.hasError || loyaltyQuery.error) {
		const errorMessage = loyaltyQuery.errorMessage || 
			(loyaltyQuery.error instanceof Error ? loyaltyQuery.error.message : 'Невідома помилка');
		
		return (
			<Box className={className}>
				<Alert severity="warning" sx={{ mb: 1 }}>
					<Typography variant="body2">
						Не вдалося завантажити інформацію про лояльність: {errorMessage}
					</Typography>
				</Alert>
			</Box>
		);
	}

	if (!loyaltyQuery.hasLoyaltySettings) {
		return null;
	}

	const loyaltySettings = loyaltyQuery.loyaltySettings;
	
	if (!loyaltySettings) {
		return null;
	}

	return (
		<Box className={className} sx={{ p: 2, bgcolor: "action.hover", borderRadius: 1 }}>
			<Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
				<DiscountIcon color="primary" fontSize="small" />
				<Typography variant="body2" fontWeight={500} color="primary">
					Програма лояльності активна
				</Typography>
				{loyaltySettings.is_enabled && (
					<Chip size="small" label="Увімкнено" color="success" variant="outlined" />
				)}
			</Box>

			{showMerchant && loyaltyQuery.hasMerchant && (
				<Box sx={{ mb: 1 }}>
					<Typography variant="body2" color="text.secondary">
						Знайдено спеціальні умови для цього мерчанта
					</Typography>
				</Box>
			)}

			{/* --- Process Check Results --- */}
			{isProcessingCheck && (
				<Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
					<CircularProgress size={16} />
					<Typography variant="body2" color="text.secondary">
						Розрахунок знижок...
					</Typography>
				</Box>
			)}

			{processCheckError && (
				<Alert severity="error" sx={{ mt: 1 }}>
					Помилка розрахунку: {processCheckError.message}
				</Alert>
			)}

			{processedCheck && (
				<Box sx={{ mt: 1, pl: 1, borderLeft: 2, borderColor: 'success.main' }}>
					{(processedCheck.discount_amount ?? 0) > 0 && (
						<Typography variant="body2" color="success.dark">
							Знижка: {formatCurrency(processedCheck.discount_amount ?? 0, currency ?? null)}
						</Typography>
					)}
					{(processedCheck.bonuses_added_amount ?? 0) > 0 && (
						<Typography variant="body2" color="success.dark">
							Буде нараховано бонусів: {processedCheck.bonuses_added_amount}
						</Typography>
					)}
					{!(processedCheck.discount_amount) && !(processedCheck.bonuses_added_amount) && (
						<Typography variant="body2" color="text.secondary">
							Додаткових знижок чи бонусів не знайдено.
						</Typography>
					)}
				</Box>
			)}
			{/* --- End of Process Check Results --- */}

		</Box>
	);
};