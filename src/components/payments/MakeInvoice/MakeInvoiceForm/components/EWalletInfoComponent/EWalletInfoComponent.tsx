import { useCallback } from "react";
import f from "@/helpers/formatText.ts";
import useLang from "@/hooks/useLang.ts";
import { mergeSx } from "@/helpers/mui.ts";
import { useAppContext } from "@/AppContext";
import Interweave from "@/features/Interweave.tsx";
import formatCurrency from "@/helpers/formatCurrency.ts";
import { Box, BoxProps, Typography } from "@mui/material";
import { EWalletInfoComponentProps } from "../../types.ts";
import { LoyaltyBlock } from "@/features/loyalty";
import { useLoyaltyDetection } from "@/hooks/loyalty";
import useGroup from "@/hooks/useGroup";

export interface EWalletInfo {
	title?: string | null;
	available_amount?: number | null | boolean;
	used_credit?: number | null;
	message?: string | null;
	credit_limit?: number | null;
	amount?: number | null;
}

export interface EWalletInfoProps extends BoxProps {
	invoiceTemplate?: EWalletInfoComponentProps["invoiceTemplate"];
	ewalletInfo?: EWalletInfo | null;
	currency?: string;
	showRechargeTitle?: boolean; // Опція для відображення заголовка про поповнення
}

export default function EWalletInfoComponent({
	currency,
	ewalletInfo: propEwalletInfo,
	invoiceTemplate,
	showRechargeTitle,
	...props
}: EWalletInfoProps) {
	const { localisation } = useAppContext();
	const lang = useLang();
	const group = useGroup();

	// Detect loyalty settings for this EWallet context
	const loyaltyQuery = useLoyaltyDetection({
		profile_id: group?.id,
		// For invoice template context, we could also pass invoice_template_id if available
		invoice_template_id: invoiceTemplate?.id,
		is_ewallet_external_payment: true,
		enabled: !!group?.id, // Only detect when group is available
	});

	let ewalletInfo;

	if (invoiceTemplate?.ewallet_user_account_info) {
		ewalletInfo = invoiceTemplate.ewallet_user_account_info;
		currency = invoiceTemplate.currency || "";
	} else if (propEwalletInfo) {
		ewalletInfo = propEwalletInfo;
	} else {
		return null;
	}

	const { message, available_amount, used_credit, title, credit_limit } = ewalletInfo;

	const renderHeader = useCallback(
		() =>
			!title ? null : (
				<Box mb={1}>
					{showRechargeTitle && (
						<Typography variant="body1" color="text.secondary">
							{localisation.payment.ewalletAccountRechargeTitle}
						</Typography>
					)}
					<Typography variant="h6">{title}</Typography>
				</Box>
			),
		[localisation.payment.ewalletAccountRechargeTitle, showRechargeTitle, title]
	);

	// if (available_amount) {
	const formattedAvailableAmount =
		typeof available_amount === "number"
			? formatCurrency(available_amount, lang, currency)
			: "∞";

	const formattedMessage = message ? message.replace(/\n/g, "<br/>") : null;

	// let creditInfo = null;
	// if (used_credit && used_credit > 0 && credit_limit) {
	// 	creditInfo = (
	// 		<Typography variant="body1" color="text.secondary">
	// 			{f(localisation.payment.ewalletUsedCreditText, {
	// 				amount: `${formatCurrency(used_credit, lang, currency)} / ${formatCurrency(credit_limit, lang, currency)}`,
	// 			})}
	// 		</Typography>
	// 	);
	// } else if (used_credit && used_credit > 0) {
	// 	creditInfo = (
	// 		<Typography variant="body1" color="text.secondary" mt={1}>
	// 			{f(localisation.payment.ewalletUsedCreditText, {
	// 				amount: formatCurrency(used_credit, lang, currency),
	// 			})}
	// 		</Typography>
	// 	);
	// } else if (credit_limit) {
	// 	creditInfo = (
	// 		<Typography variant="body1" color="text.secondary" mt={1}>
	// 			{f(localisation.payment.incustPayCreditLimitText, {
	// 				amount: formatCurrency(credit_limit, lang, currency),
	// 			})}
	// 		</Typography>
	// 	);
	// }

	return (
		<Box
			{...props}
			sx={mergeSx(
				{
					p: 3,
					// mb: 2,
					borderBottom: 1,
					borderColor: "divider",
					textAlign: "center",
				},
				props.sx
			)}
		>
			{renderHeader()}

			{/* Show loyalty information if available */}
			<LoyaltyBlock loyaltyQuery={loyaltyQuery} />

			{formattedMessage && !available_amount && (
				<Box>
					<Interweave content={formattedMessage} />
				</Box>
			)}

			<Box sx={{ width: "80%", mx: "auto", mb: 1, textAlign: "center" }}>
				{!propEwalletInfo?.title && (
					<Typography variant="body1">
						{localisation.ewalletPay.availableAmountLabel}
					</Typography>
				)}

				<Typography variant="h4" component={"div"} fontWeight="bold">
					{formattedAvailableAmount}
				</Typography>
				{/* {creditInfo} */}

				{Boolean(credit_limit && credit_limit > 0) && (
					<Typography variant="body2" color="text.secondary">
						{f(`${localisation.ewalletPay.creditLimitLabel}`, {
							amount: formatCurrency(credit_limit || 0, lang, currency),
						})}
					</Typography>
				)}

				{Boolean(credit_limit) && (
					<Typography variant="body2" color="text.secondary">
						{f(localisation.ewalletPay.ewalletPayOnHandAmountText, {
							amount: formatCurrency(
								!!propEwalletInfo?.amount && propEwalletInfo?.amount > 0
									? propEwalletInfo?.amount
									: 0,
								lang,
								currency
							),
						})}
					</Typography>
				)}

				{Boolean(used_credit && used_credit > 0) && (
					<Typography variant="body2" color="text.secondary">
						{f(localisation.payment.ewalletUsedCreditText, {
							amount: formatCurrency(used_credit || 0, lang, currency),
						})}
					</Typography>
				)}
			</Box>
		</Box>
	);
	// }

	// Якщо немає available_amount або воно дорівнює 0, але є кредит або повідомлення
	// Перевіряємо кредитний ліміт (навіть якщо available_amount = 0)
	// if (!available_amount || available_amount === 0) {
	// 	// Форматування інформації про кредит якщо немає доступної суми
	// 	let creditInfo = null;
	// 	if (used_credit && used_credit > 0 && credit_limit) {
	// 		creditInfo = (
	// 			<Typography variant="body1" mt={2}>
	// 				{f(localisation.payment.ewalletUsedCreditText, {
	// 					amount: `${formatCurrency(used_credit, lang, currency)} / ${formatCurrency(credit_limit, lang, currency)}`,
	// 				})}
	// 			</Typography>
	// 		);
	// 	} else if (used_credit && used_credit > 0) {
	// 		creditInfo = (
	// 			<Typography variant="body1" mt={2}>
	// 				{f(localisation.payment.ewalletUsedCreditText, {
	// 					amount: formatCurrency(used_credit, lang, currency),
	// 				})}
	// 			</Typography>
	// 		);
	// 	} else if (credit_limit) {
	// 		creditInfo = (
	// 			<Typography variant="body1" mt={2}>
	// 				{f(localisation.payment.incustPayCreditLimitText, {
	// 					amount: formatCurrency(credit_limit, lang, currency),
	// 				})}
	// 			</Typography>
	// 		);
	// 	}

	// 	const hasContent = message || creditInfo;
	// 	if (!hasContent) return null;

	// 	const formattedMessage = message ? message.replace(/\n/g, "<br/>") : null;

	// 	return (
	// 		<Box sx={{ p: 3, mb: 2, borderBottom: 1, borderColor: "divider", textAlign: "center" }}>
	// 			{renderHeader()}

	// 			{formattedMessage && (
	// 				<Box>
	// 					<Interweave content={formattedMessage} />
	// 				</Box>
	// 			)}

	// 			{creditInfo}
	// 		</Box>
	// 	);
	// }

	return null;
}
