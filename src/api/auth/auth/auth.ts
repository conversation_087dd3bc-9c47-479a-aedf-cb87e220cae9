/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Auth API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	AuthorisedResponse,
	ChangeEmailData,
	CheckIsChatIdExistsParams,
	ConfirmEmailBody,
	ConfirmEmailRequestSent,
	ConfirmEmailResult,
	GetTokenByApple200,
	GetTokenByGoogle200,
	HTTPValidationError,
	HeartbeatParams,
	IsChatIDExists,
	IsEmailConfirmedData,
	IsEmailExists,
	LoginByAuthTokenData,
	LoginByShortTokenData,
	LoginByShortTokenResult,
	LoginByWebViewData,
	LoginByWebViewNewData,
	LoginData,
	MarketingConsentSchema,
	MarketingInfo,
	NewLoginByWebViewData,
	NewLoginData,
	NewOauthRegisterToken,
	NewRegisterData,
	OAuthAppleLoginToken,
	OAuthLoginToken,
	OAuthRegisterData,
	OAuthRegisterToken,
	OkResponse,
	RegisterData,
	ResetPasswordData,
	SendConfirmEmailData,
	SetPushTokenData,
	Token,
	ValidateTokenResult,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Validate Token
 */
export const validateToken = (signal?: AbortSignal) => {
	return getAxios<ValidateTokenResult>({ url: `/auth/validateToken`, method: "POST", signal });
};

export const getValidateTokenMutationOptions = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof validateToken>>,
		TError,
		void,
		TContext
	>;
}): UseMutationOptions<Awaited<ReturnType<typeof validateToken>>, TError, void, TContext> => {
	const mutationKey = ["validateToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<Awaited<ReturnType<typeof validateToken>>, void> = () => {
		return validateToken();
	};

	return { mutationFn, ...mutationOptions };
};

export type ValidateTokenMutationResult = NonNullable<Awaited<ReturnType<typeof validateToken>>>;

export type ValidateTokenMutationError = ErrorType<unknown>;

/**
 * @summary Validate Token
 */
export const useValidateToken = <TError = ErrorType<unknown>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof validateToken>>,
		TError,
		void,
		TContext
	>;
}): UseMutationResult<Awaited<ReturnType<typeof validateToken>>, TError, void, TContext> => {
	const mutationOptions = getValidateTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Send Confirm Email
 */
export const sendConfirmEmail = (
	sendConfirmEmailData: SendConfirmEmailData,
	signal?: AbortSignal
) => {
	return getAxios<ConfirmEmailRequestSent>({
		url: `/auth/sendConfirmEmail`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: sendConfirmEmailData,
		signal,
	});
};

export const getSendConfirmEmailMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendConfirmEmail>>,
		TError,
		{ data: SendConfirmEmailData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof sendConfirmEmail>>,
	TError,
	{ data: SendConfirmEmailData },
	TContext
> => {
	const mutationKey = ["sendConfirmEmail"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof sendConfirmEmail>>,
		{ data: SendConfirmEmailData }
	> = props => {
		const { data } = props ?? {};

		return sendConfirmEmail(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type SendConfirmEmailMutationResult = NonNullable<
	Awaited<ReturnType<typeof sendConfirmEmail>>
>;
export type SendConfirmEmailMutationBody = SendConfirmEmailData;
export type SendConfirmEmailMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Send Confirm Email
 */
export const useSendConfirmEmail = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendConfirmEmail>>,
		TError,
		{ data: SendConfirmEmailData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof sendConfirmEmail>>,
	TError,
	{ data: SendConfirmEmailData },
	TContext
> => {
	const mutationOptions = getSendConfirmEmailMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Confirm Email
 */
export const confirmEmail = (confirmEmailBody: ConfirmEmailBody, signal?: AbortSignal) => {
	return getAxios<ConfirmEmailResult>({
		url: `/auth/confirmEmail`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: confirmEmailBody,
		signal,
	});
};

export const getConfirmEmailMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof confirmEmail>>,
		TError,
		{ data: ConfirmEmailBody },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof confirmEmail>>,
	TError,
	{ data: ConfirmEmailBody },
	TContext
> => {
	const mutationKey = ["confirmEmail"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof confirmEmail>>,
		{ data: ConfirmEmailBody }
	> = props => {
		const { data } = props ?? {};

		return confirmEmail(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type ConfirmEmailMutationResult = NonNullable<Awaited<ReturnType<typeof confirmEmail>>>;
export type ConfirmEmailMutationBody = ConfirmEmailBody;
export type ConfirmEmailMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Confirm Email
 */
export const useConfirmEmail = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof confirmEmail>>,
		TError,
		{ data: ConfirmEmailBody },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof confirmEmail>>,
	TError,
	{ data: ConfirmEmailBody },
	TContext
> => {
	const mutationOptions = getConfirmEmailMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Check Is Email Confirmed
 */
export const checkIsEmailConfirmed = (signal?: AbortSignal) => {
	return getAxios<IsEmailConfirmedData>({
		url: `/auth/checkIsEmailConfirmed`,
		method: "GET",
		signal,
	});
};

export const getCheckIsEmailConfirmedQueryKey = () => {
	return [`/auth/checkIsEmailConfirmed`] as const;
};

export const getCheckIsEmailConfirmedQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsEmailConfirmedQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsEmailConfirmed>>> = ({
		signal,
	}) => checkIsEmailConfirmed(signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsEmailConfirmedQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsEmailConfirmed>>
>;
export type CheckIsEmailConfirmedQueryError = ErrorType<unknown>;

export function useCheckIsEmailConfirmed<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	> &
		Pick<
			DefinedInitialDataOptions<
				Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
				TError,
				Awaited<ReturnType<typeof checkIsEmailConfirmed>>
			>,
			"initialData"
		>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsEmailConfirmed<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	> &
		Pick<
			UndefinedInitialDataOptions<
				Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
				TError,
				Awaited<ReturnType<typeof checkIsEmailConfirmed>>
			>,
			"initialData"
		>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsEmailConfirmed<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Email Confirmed
 */

export function useCheckIsEmailConfirmed<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsEmailConfirmedQueryOptions(options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckIsEmailConfirmedSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsEmailConfirmedQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsEmailConfirmed>>> = ({
		signal,
	}) => checkIsEmailConfirmed(signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsEmailConfirmedSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsEmailConfirmed>>
>;
export type CheckIsEmailConfirmedSuspenseQueryError = ErrorType<unknown>;

export function useCheckIsEmailConfirmedSuspense<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsEmailConfirmedSuspense<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsEmailConfirmedSuspense<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Email Confirmed
 */

export function useCheckIsEmailConfirmedSuspense<
	TData = Awaited<ReturnType<typeof checkIsEmailConfirmed>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsEmailConfirmed>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsEmailConfirmedSuspenseQueryOptions(options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Login
 */
export const login = (loginData: LoginData, signal?: AbortSignal) => {
	return getAxios<Token>({
		url: `/auth/login`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: loginData,
		signal,
	});
};

export const getLoginMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof login>>,
		TError,
		{ data: LoginData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof login>>,
	TError,
	{ data: LoginData },
	TContext
> => {
	const mutationKey = ["login"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof login>>,
		{ data: LoginData }
	> = props => {
		const { data } = props ?? {};

		return login(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type LoginMutationResult = NonNullable<Awaited<ReturnType<typeof login>>>;
export type LoginMutationBody = LoginData;
export type LoginMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Login
 */
export const useLogin = <TError = ErrorType<HTTPValidationError>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof login>>,
		TError,
		{ data: LoginData },
		TContext
	>;
}): UseMutationResult<Awaited<ReturnType<typeof login>>, TError, { data: LoginData }, TContext> => {
	const mutationOptions = getLoginMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary New Login
 */
export const newLogin = (newLoginData: NewLoginData, signal?: AbortSignal) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/new_login`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: newLoginData,
		signal,
	});
};

export const getNewLoginMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newLogin>>,
		TError,
		{ data: NewLoginData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof newLogin>>,
	TError,
	{ data: NewLoginData },
	TContext
> => {
	const mutationKey = ["newLogin"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof newLogin>>,
		{ data: NewLoginData }
	> = props => {
		const { data } = props ?? {};

		return newLogin(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type NewLoginMutationResult = NonNullable<Awaited<ReturnType<typeof newLogin>>>;
export type NewLoginMutationBody = NewLoginData;
export type NewLoginMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary New Login
 */
export const useNewLogin = <TError = ErrorType<HTTPValidationError>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newLogin>>,
		TError,
		{ data: NewLoginData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof newLogin>>,
	TError,
	{ data: NewLoginData },
	TContext
> => {
	const mutationOptions = getNewLoginMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Logout
 */
export const logout = (signal?: AbortSignal) => {
	return getAxios<OkResponse>({ url: `/auth/logout`, method: "POST", signal });
};

export const getLogoutMutationOptions = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<Awaited<ReturnType<typeof logout>>, TError, void, TContext>;
}): UseMutationOptions<Awaited<ReturnType<typeof logout>>, TError, void, TContext> => {
	const mutationKey = ["logout"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<Awaited<ReturnType<typeof logout>>, void> = () => {
		return logout();
	};

	return { mutationFn, ...mutationOptions };
};

export type LogoutMutationResult = NonNullable<Awaited<ReturnType<typeof logout>>>;

export type LogoutMutationError = ErrorType<unknown>;

/**
 * @summary Logout
 */
export const useLogout = <TError = ErrorType<unknown>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<Awaited<ReturnType<typeof logout>>, TError, void, TContext>;
}): UseMutationResult<Awaited<ReturnType<typeof logout>>, TError, void, TContext> => {
	const mutationOptions = getLogoutMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Set Push Token
 */
export const setPushToken = (setPushTokenData: SetPushTokenData, signal?: AbortSignal) => {
	return getAxios<OkResponse>({
		url: `/auth/set_push_token`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: setPushTokenData,
		signal,
	});
};

export const getSetPushTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof setPushToken>>,
		TError,
		{ data: SetPushTokenData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof setPushToken>>,
	TError,
	{ data: SetPushTokenData },
	TContext
> => {
	const mutationKey = ["setPushToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof setPushToken>>,
		{ data: SetPushTokenData }
	> = props => {
		const { data } = props ?? {};

		return setPushToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type SetPushTokenMutationResult = NonNullable<Awaited<ReturnType<typeof setPushToken>>>;
export type SetPushTokenMutationBody = SetPushTokenData;
export type SetPushTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Set Push Token
 */
export const useSetPushToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof setPushToken>>,
		TError,
		{ data: SetPushTokenData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof setPushToken>>,
	TError,
	{ data: SetPushTokenData },
	TContext
> => {
	const mutationOptions = getSetPushTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Refresh Token
 */
export const refreshToken = (signal?: AbortSignal) => {
	return getAxios<AuthorisedResponse>({ url: `/auth/refresh_token`, method: "POST", signal });
};

export const getRefreshTokenMutationOptions = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<Awaited<ReturnType<typeof refreshToken>>, TError, void, TContext>;
}): UseMutationOptions<Awaited<ReturnType<typeof refreshToken>>, TError, void, TContext> => {
	const mutationKey = ["refreshToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<Awaited<ReturnType<typeof refreshToken>>, void> = () => {
		return refreshToken();
	};

	return { mutationFn, ...mutationOptions };
};

export type RefreshTokenMutationResult = NonNullable<Awaited<ReturnType<typeof refreshToken>>>;

export type RefreshTokenMutationError = ErrorType<unknown>;

/**
 * @summary Refresh Token
 */
export const useRefreshToken = <TError = ErrorType<unknown>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<Awaited<ReturnType<typeof refreshToken>>, TError, void, TContext>;
}): UseMutationResult<Awaited<ReturnType<typeof refreshToken>>, TError, void, TContext> => {
	const mutationOptions = getRefreshTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns auth token
 * @summary Get Tg Webview Token
 */
export const getTgWebviewToken = (loginByWebViewData: LoginByWebViewData, signal?: AbortSignal) => {
	return getAxios<Token>({
		url: `/auth/login/tg_webview`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: loginByWebViewData,
		signal,
	});
};

export const getGetTgWebviewTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTgWebviewToken>>,
		TError,
		{ data: LoginByWebViewData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getTgWebviewToken>>,
	TError,
	{ data: LoginByWebViewData },
	TContext
> => {
	const mutationKey = ["getTgWebviewToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getTgWebviewToken>>,
		{ data: LoginByWebViewData }
	> = props => {
		const { data } = props ?? {};

		return getTgWebviewToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetTgWebviewTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof getTgWebviewToken>>
>;
export type GetTgWebviewTokenMutationBody = LoginByWebViewData;
export type GetTgWebviewTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Tg Webview Token
 */
export const useGetTgWebviewToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTgWebviewToken>>,
		TError,
		{ data: LoginByWebViewData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getTgWebviewToken>>,
	TError,
	{ data: LoginByWebViewData },
	TContext
> => {
	const mutationOptions = getGetTgWebviewTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns auth token
 * @summary New Get Tg Webview Token
 */
export const newGetTgWebviewToken = (
	newLoginByWebViewData: NewLoginByWebViewData,
	signal?: AbortSignal
) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/new_login/tg_webview`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: newLoginByWebViewData,
		signal,
	});
};

export const getNewGetTgWebviewTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newGetTgWebviewToken>>,
		TError,
		{ data: NewLoginByWebViewData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof newGetTgWebviewToken>>,
	TError,
	{ data: NewLoginByWebViewData },
	TContext
> => {
	const mutationKey = ["newGetTgWebviewToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof newGetTgWebviewToken>>,
		{ data: NewLoginByWebViewData }
	> = props => {
		const { data } = props ?? {};

		return newGetTgWebviewToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type NewGetTgWebviewTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof newGetTgWebviewToken>>
>;
export type NewGetTgWebviewTokenMutationBody = NewLoginByWebViewData;
export type NewGetTgWebviewTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary New Get Tg Webview Token
 */
export const useNewGetTgWebviewToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newGetTgWebviewToken>>,
		TError,
		{ data: NewLoginByWebViewData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof newGetTgWebviewToken>>,
	TError,
	{ data: NewLoginByWebViewData },
	TContext
> => {
	const mutationOptions = getNewGetTgWebviewTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Login By Tg Webview New
 */
export const loginByTgWebviewNew = (
	loginByWebViewNewData: LoginByWebViewNewData,
	signal?: AbortSignal
) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/login/tg_webview_new`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: loginByWebViewNewData,
		signal,
	});
};

export const getLoginByTgWebviewNewMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof loginByTgWebviewNew>>,
		TError,
		{ data: LoginByWebViewNewData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof loginByTgWebviewNew>>,
	TError,
	{ data: LoginByWebViewNewData },
	TContext
> => {
	const mutationKey = ["loginByTgWebviewNew"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof loginByTgWebviewNew>>,
		{ data: LoginByWebViewNewData }
	> = props => {
		const { data } = props ?? {};

		return loginByTgWebviewNew(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type LoginByTgWebviewNewMutationResult = NonNullable<
	Awaited<ReturnType<typeof loginByTgWebviewNew>>
>;
export type LoginByTgWebviewNewMutationBody = LoginByWebViewNewData;
export type LoginByTgWebviewNewMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Login By Tg Webview New
 */
export const useLoginByTgWebviewNew = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof loginByTgWebviewNew>>,
		TError,
		{ data: LoginByWebViewNewData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof loginByTgWebviewNew>>,
	TError,
	{ data: LoginByWebViewNewData },
	TContext
> => {
	const mutationOptions = getLoginByTgWebviewNewMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns auth token
 * @summary Get Token By Short Token
 */
export const getTokenByShortToken = (
	loginByShortTokenData: LoginByShortTokenData,
	signal?: AbortSignal
) => {
	return getAxios<LoginByShortTokenResult>({
		url: `/auth/login/by_short_token`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: loginByShortTokenData,
		signal,
	});
};

export const getGetTokenByShortTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByShortToken>>,
		TError,
		{ data: LoginByShortTokenData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getTokenByShortToken>>,
	TError,
	{ data: LoginByShortTokenData },
	TContext
> => {
	const mutationKey = ["getTokenByShortToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getTokenByShortToken>>,
		{ data: LoginByShortTokenData }
	> = props => {
		const { data } = props ?? {};

		return getTokenByShortToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetTokenByShortTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof getTokenByShortToken>>
>;
export type GetTokenByShortTokenMutationBody = LoginByShortTokenData;
export type GetTokenByShortTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Token By Short Token
 */
export const useGetTokenByShortToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByShortToken>>,
		TError,
		{ data: LoginByShortTokenData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getTokenByShortToken>>,
	TError,
	{ data: LoginByShortTokenData },
	TContext
> => {
	const mutationOptions = getGetTokenByShortTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns auth token
 * @summary Get Token By Google
 */
export const getTokenByGoogle = (oAuthLoginToken: OAuthLoginToken, signal?: AbortSignal) => {
	return getAxios<GetTokenByGoogle200>({
		url: `/auth/login/by_google`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: oAuthLoginToken,
		signal,
	});
};

export const getGetTokenByGoogleMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByGoogle>>,
		TError,
		{ data: OAuthLoginToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getTokenByGoogle>>,
	TError,
	{ data: OAuthLoginToken },
	TContext
> => {
	const mutationKey = ["getTokenByGoogle"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getTokenByGoogle>>,
		{ data: OAuthLoginToken }
	> = props => {
		const { data } = props ?? {};

		return getTokenByGoogle(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetTokenByGoogleMutationResult = NonNullable<
	Awaited<ReturnType<typeof getTokenByGoogle>>
>;
export type GetTokenByGoogleMutationBody = OAuthLoginToken;
export type GetTokenByGoogleMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Token By Google
 */
export const useGetTokenByGoogle = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByGoogle>>,
		TError,
		{ data: OAuthLoginToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getTokenByGoogle>>,
	TError,
	{ data: OAuthLoginToken },
	TContext
> => {
	const mutationOptions = getGetTokenByGoogleMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns auth token
 * @summary Get Token By Apple
 */
export const getTokenByApple = (
	oAuthAppleLoginToken: OAuthAppleLoginToken,
	signal?: AbortSignal
) => {
	return getAxios<GetTokenByApple200>({
		url: `/auth/login/by_apple`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: oAuthAppleLoginToken,
		signal,
	});
};

export const getGetTokenByAppleMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByApple>>,
		TError,
		{ data: OAuthAppleLoginToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getTokenByApple>>,
	TError,
	{ data: OAuthAppleLoginToken },
	TContext
> => {
	const mutationKey = ["getTokenByApple"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getTokenByApple>>,
		{ data: OAuthAppleLoginToken }
	> = props => {
		const { data } = props ?? {};

		return getTokenByApple(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetTokenByAppleMutationResult = NonNullable<
	Awaited<ReturnType<typeof getTokenByApple>>
>;
export type GetTokenByAppleMutationBody = OAuthAppleLoginToken;
export type GetTokenByAppleMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Token By Apple
 */
export const useGetTokenByApple = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getTokenByApple>>,
		TError,
		{ data: OAuthAppleLoginToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getTokenByApple>>,
	TError,
	{ data: OAuthAppleLoginToken },
	TContext
> => {
	const mutationOptions = getGetTokenByAppleMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Login By Auth Token
 */
export const loginByAuthToken = (
	loginByAuthTokenData: LoginByAuthTokenData,
	signal?: AbortSignal
) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/login/by_auth_token`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: loginByAuthTokenData,
		signal,
	});
};

export const getLoginByAuthTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof loginByAuthToken>>,
		TError,
		{ data: LoginByAuthTokenData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof loginByAuthToken>>,
	TError,
	{ data: LoginByAuthTokenData },
	TContext
> => {
	const mutationKey = ["loginByAuthToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof loginByAuthToken>>,
		{ data: LoginByAuthTokenData }
	> = props => {
		const { data } = props ?? {};

		return loginByAuthToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type LoginByAuthTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof loginByAuthToken>>
>;
export type LoginByAuthTokenMutationBody = LoginByAuthTokenData;
export type LoginByAuthTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Login By Auth Token
 */
export const useLoginByAuthToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof loginByAuthToken>>,
		TError,
		{ data: LoginByAuthTokenData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof loginByAuthToken>>,
	TError,
	{ data: LoginByAuthTokenData },
	TContext
> => {
	const mutationOptions = getLoginByAuthTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Generate User Auth Token
 */
export const generateUserAuthToken = (signal?: AbortSignal) => {
	return getAxios<Token>({ url: `/auth/generateAuthToken`, method: "POST", signal });
};

export const getGenerateUserAuthTokenMutationOptions = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof generateUserAuthToken>>,
		TError,
		void,
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof generateUserAuthToken>>,
	TError,
	void,
	TContext
> => {
	const mutationKey = ["generateUserAuthToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof generateUserAuthToken>>,
		void
	> = () => {
		return generateUserAuthToken();
	};

	return { mutationFn, ...mutationOptions };
};

export type GenerateUserAuthTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof generateUserAuthToken>>
>;

export type GenerateUserAuthTokenMutationError = ErrorType<unknown>;

/**
 * @summary Generate User Auth Token
 */
export const useGenerateUserAuthToken = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof generateUserAuthToken>>,
		TError,
		void,
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof generateUserAuthToken>>,
	TError,
	void,
	TContext
> => {
	const mutationOptions = getGenerateUserAuthTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Register
 */
export const register = (registerData: RegisterData, signal?: AbortSignal) => {
	return getAxios<Token>({
		url: `/auth/register`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: registerData,
		signal,
	});
};

export const getRegisterMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof register>>,
		TError,
		{ data: RegisterData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof register>>,
	TError,
	{ data: RegisterData },
	TContext
> => {
	const mutationKey = ["register"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof register>>,
		{ data: RegisterData }
	> = props => {
		const { data } = props ?? {};

		return register(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type RegisterMutationResult = NonNullable<Awaited<ReturnType<typeof register>>>;
export type RegisterMutationBody = RegisterData;
export type RegisterMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Register
 */
export const useRegister = <TError = ErrorType<HTTPValidationError>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof register>>,
		TError,
		{ data: RegisterData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof register>>,
	TError,
	{ data: RegisterData },
	TContext
> => {
	const mutationOptions = getRegisterMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary New Register
 */
export const newRegister = (newRegisterData: NewRegisterData, signal?: AbortSignal) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/new_register`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: newRegisterData,
		signal,
	});
};

export const getNewRegisterMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newRegister>>,
		TError,
		{ data: NewRegisterData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof newRegister>>,
	TError,
	{ data: NewRegisterData },
	TContext
> => {
	const mutationKey = ["newRegister"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof newRegister>>,
		{ data: NewRegisterData }
	> = props => {
		const { data } = props ?? {};

		return newRegister(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type NewRegisterMutationResult = NonNullable<Awaited<ReturnType<typeof newRegister>>>;
export type NewRegisterMutationBody = NewRegisterData;
export type NewRegisterMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary New Register
 */
export const useNewRegister = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newRegister>>,
		TError,
		{ data: NewRegisterData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof newRegister>>,
	TError,
	{ data: NewRegisterData },
	TContext
> => {
	const mutationOptions = getNewRegisterMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Register Oauth
 */
export const registerOauth = (oAuthRegisterToken: OAuthRegisterToken, signal?: AbortSignal) => {
	return getAxios<Token>({
		url: `/auth/register/oauth`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: oAuthRegisterToken,
		signal,
	});
};

export const getRegisterOauthMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof registerOauth>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof registerOauth>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationKey = ["registerOauth"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof registerOauth>>,
		{ data: OAuthRegisterToken }
	> = props => {
		const { data } = props ?? {};

		return registerOauth(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type RegisterOauthMutationResult = NonNullable<Awaited<ReturnType<typeof registerOauth>>>;
export type RegisterOauthMutationBody = OAuthRegisterToken;
export type RegisterOauthMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Register Oauth
 */
export const useRegisterOauth = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof registerOauth>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof registerOauth>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationOptions = getRegisterOauthMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Customer Consent
 */
export const customerConsent = (oAuthRegisterToken: OAuthRegisterToken, signal?: AbortSignal) => {
	return getAxios<Token>({
		url: `/auth/customer_consent/oauth`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: oAuthRegisterToken,
		signal,
	});
};

export const getCustomerConsentMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof customerConsent>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof customerConsent>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationKey = ["customerConsent"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof customerConsent>>,
		{ data: OAuthRegisterToken }
	> = props => {
		const { data } = props ?? {};

		return customerConsent(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type CustomerConsentMutationResult = NonNullable<
	Awaited<ReturnType<typeof customerConsent>>
>;
export type CustomerConsentMutationBody = OAuthRegisterToken;
export type CustomerConsentMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Customer Consent
 */
export const useCustomerConsent = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof customerConsent>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof customerConsent>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationOptions = getCustomerConsentMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary New Customer Consent
 */
export const newCustomerConsent = (
	newOauthRegisterToken: NewOauthRegisterToken,
	signal?: AbortSignal
) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/new_customer_consent/oauth`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: newOauthRegisterToken,
		signal,
	});
};

export const getNewCustomerConsentMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newCustomerConsent>>,
		TError,
		{ data: NewOauthRegisterToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof newCustomerConsent>>,
	TError,
	{ data: NewOauthRegisterToken },
	TContext
> => {
	const mutationKey = ["newCustomerConsent"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof newCustomerConsent>>,
		{ data: NewOauthRegisterToken }
	> = props => {
		const { data } = props ?? {};

		return newCustomerConsent(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type NewCustomerConsentMutationResult = NonNullable<
	Awaited<ReturnType<typeof newCustomerConsent>>
>;
export type NewCustomerConsentMutationBody = NewOauthRegisterToken;
export type NewCustomerConsentMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary New Customer Consent
 */
export const useNewCustomerConsent = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newCustomerConsent>>,
		TError,
		{ data: NewOauthRegisterToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof newCustomerConsent>>,
	TError,
	{ data: NewOauthRegisterToken },
	TContext
> => {
	const mutationOptions = getNewCustomerConsentMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary New Register Oauth
 */
export const newRegisterOauth = (
	newOauthRegisterToken: NewOauthRegisterToken,
	signal?: AbortSignal
) => {
	return getAxios<AuthorisedResponse>({
		url: `/auth/new_register/oauth`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: newOauthRegisterToken,
		signal,
	});
};

export const getNewRegisterOauthMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newRegisterOauth>>,
		TError,
		{ data: NewOauthRegisterToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof newRegisterOauth>>,
	TError,
	{ data: NewOauthRegisterToken },
	TContext
> => {
	const mutationKey = ["newRegisterOauth"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof newRegisterOauth>>,
		{ data: NewOauthRegisterToken }
	> = props => {
		const { data } = props ?? {};

		return newRegisterOauth(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type NewRegisterOauthMutationResult = NonNullable<
	Awaited<ReturnType<typeof newRegisterOauth>>
>;
export type NewRegisterOauthMutationBody = NewOauthRegisterToken;
export type NewRegisterOauthMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary New Register Oauth
 */
export const useNewRegisterOauth = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof newRegisterOauth>>,
		TError,
		{ data: NewOauthRegisterToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof newRegisterOauth>>,
	TError,
	{ data: NewOauthRegisterToken },
	TContext
> => {
	const mutationOptions = getNewRegisterOauthMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Check Is Email Exists
 */
export const checkIsEmailExists = (email: string | undefined | null, signal?: AbortSignal) => {
	return getAxios<IsEmailExists>({
		url: `/auth/checkIsEmailExists/${email}`,
		method: "POST",
		signal,
	});
};

export const getCheckIsEmailExistsMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof checkIsEmailExists>>,
		TError,
		{ email: string | undefined | null },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof checkIsEmailExists>>,
	TError,
	{ email: string | undefined | null },
	TContext
> => {
	const mutationKey = ["checkIsEmailExists"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof checkIsEmailExists>>,
		{ email: string | undefined | null }
	> = props => {
		const { email } = props ?? {};

		return checkIsEmailExists(email);
	};

	return { mutationFn, ...mutationOptions };
};

export type CheckIsEmailExistsMutationResult = NonNullable<
	Awaited<ReturnType<typeof checkIsEmailExists>>
>;

export type CheckIsEmailExistsMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Check Is Email Exists
 */
export const useCheckIsEmailExists = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof checkIsEmailExists>>,
		TError,
		{ email: string | undefined | null },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof checkIsEmailExists>>,
	TError,
	{ email: string | undefined | null },
	TContext
> => {
	const mutationOptions = getCheckIsEmailExistsMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Check Is Chat Id Exists
 */
export const checkIsChatIdExists = (params: CheckIsChatIdExistsParams, signal?: AbortSignal) => {
	return getAxios<IsChatIDExists>({
		url: `/auth/checkIsChatIdExists`,
		method: "GET",
		params,
		signal,
	});
};

export const getCheckIsChatIdExistsQueryKey = (params: CheckIsChatIdExistsParams) => {
	return [`/auth/checkIsChatIdExists`, ...(params ? [params] : [])] as const;
};

export const getCheckIsChatIdExistsQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsChatIdExistsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsChatIdExists>>> = ({ signal }) =>
		checkIsChatIdExists(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkIsChatIdExists>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsChatIdExistsQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsChatIdExists>>
>;
export type CheckIsChatIdExistsQueryError = ErrorType<HTTPValidationError>;

export function useCheckIsChatIdExists<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkIsChatIdExists>>,
					TError,
					Awaited<ReturnType<typeof checkIsChatIdExists>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsChatIdExists<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkIsChatIdExists>>,
					TError,
					Awaited<ReturnType<typeof checkIsChatIdExists>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsChatIdExists<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Chat Id Exists
 */

export function useCheckIsChatIdExists<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsChatIdExistsQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckIsChatIdExistsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsChatIdExistsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsChatIdExists>>> = ({ signal }) =>
		checkIsChatIdExists(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkIsChatIdExists>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsChatIdExistsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsChatIdExists>>
>;
export type CheckIsChatIdExistsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useCheckIsChatIdExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsChatIdExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsChatIdExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Chat Id Exists
 */

export function useCheckIsChatIdExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsChatIdExists>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckIsChatIdExistsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkIsChatIdExists>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsChatIdExistsSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Reset Password
 */
export const resetPassword = (resetPasswordData: ResetPasswordData, signal?: AbortSignal) => {
	return getAxios<unknown>({
		url: `/auth/resetPassword`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: resetPasswordData,
		signal,
	});
};

export const getResetPasswordMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof resetPassword>>,
		TError,
		{ data: ResetPasswordData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof resetPassword>>,
	TError,
	{ data: ResetPasswordData },
	TContext
> => {
	const mutationKey = ["resetPassword"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof resetPassword>>,
		{ data: ResetPasswordData }
	> = props => {
		const { data } = props ?? {};

		return resetPassword(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type ResetPasswordMutationResult = NonNullable<Awaited<ReturnType<typeof resetPassword>>>;
export type ResetPasswordMutationBody = ResetPasswordData;
export type ResetPasswordMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Reset Password
 */
export const useResetPassword = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof resetPassword>>,
		TError,
		{ data: ResetPasswordData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof resetPassword>>,
	TError,
	{ data: ResetPasswordData },
	TContext
> => {
	const mutationOptions = getResetPasswordMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Change Email
 */
export const changeEmail = (changeEmailData: ChangeEmailData, signal?: AbortSignal) => {
	return getAxios<unknown>({
		url: `/auth/changeEmail`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: changeEmailData,
		signal,
	});
};

export const getChangeEmailMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof changeEmail>>,
		TError,
		{ data: ChangeEmailData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof changeEmail>>,
	TError,
	{ data: ChangeEmailData },
	TContext
> => {
	const mutationKey = ["changeEmail"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof changeEmail>>,
		{ data: ChangeEmailData }
	> = props => {
		const { data } = props ?? {};

		return changeEmail(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type ChangeEmailMutationResult = NonNullable<Awaited<ReturnType<typeof changeEmail>>>;
export type ChangeEmailMutationBody = ChangeEmailData;
export type ChangeEmailMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Change Email
 */
export const useChangeEmail = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof changeEmail>>,
		TError,
		{ data: ChangeEmailData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof changeEmail>>,
	TError,
	{ data: ChangeEmailData },
	TContext
> => {
	const mutationOptions = getChangeEmailMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Apple Oauth Callback
 */
export const appleOauthCallback = (signal?: AbortSignal) => {
	return getAxios<unknown>({ url: `/auth/apple_oauth`, method: "POST", signal });
};

export const getAppleOauthCallbackMutationOptions = <
	TError = ErrorType<unknown>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof appleOauthCallback>>,
		TError,
		void,
		TContext
	>;
}): UseMutationOptions<Awaited<ReturnType<typeof appleOauthCallback>>, TError, void, TContext> => {
	const mutationKey = ["appleOauthCallback"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof appleOauthCallback>>,
		void
	> = () => {
		return appleOauthCallback();
	};

	return { mutationFn, ...mutationOptions };
};

export type AppleOauthCallbackMutationResult = NonNullable<
	Awaited<ReturnType<typeof appleOauthCallback>>
>;

export type AppleOauthCallbackMutationError = ErrorType<unknown>;

/**
 * @summary Apple Oauth Callback
 */
export const useAppleOauthCallback = <TError = ErrorType<unknown>, TContext = unknown>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof appleOauthCallback>>,
		TError,
		void,
		TContext
	>;
}): UseMutationResult<Awaited<ReturnType<typeof appleOauthCallback>>, TError, void, TContext> => {
	const mutationOptions = getAppleOauthCallbackMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get User Info By Oauth Token
 */
export const getUserInfoByOauthToken = (
	oAuthRegisterToken: OAuthRegisterToken,
	signal?: AbortSignal
) => {
	return getAxios<OAuthRegisterData>({
		url: `/auth/oauth/user_info`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: oAuthRegisterToken,
		signal,
	});
};

export const getGetUserInfoByOauthTokenMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getUserInfoByOauthToken>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getUserInfoByOauthToken>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationKey = ["getUserInfoByOauthToken"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getUserInfoByOauthToken>>,
		{ data: OAuthRegisterToken }
	> = props => {
		const { data } = props ?? {};

		return getUserInfoByOauthToken(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetUserInfoByOauthTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof getUserInfoByOauthToken>>
>;
export type GetUserInfoByOauthTokenMutationBody = OAuthRegisterToken;
export type GetUserInfoByOauthTokenMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get User Info By Oauth Token
 */
export const useGetUserInfoByOauthToken = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getUserInfoByOauthToken>>,
		TError,
		{ data: OAuthRegisterToken },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getUserInfoByOauthToken>>,
	TError,
	{ data: OAuthRegisterToken },
	TContext
> => {
	const mutationOptions = getGetUserInfoByOauthTokenMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Heartbeat
 */
export const heartbeat = (params: HeartbeatParams, signal?: AbortSignal) => {
	return getAxios<OkResponse>({ url: `/auth/heartbeat`, method: "GET", params, signal });
};

export const getHeartbeatQueryKey = (params: HeartbeatParams) => {
	return [`/auth/heartbeat`, ...(params ? [params] : [])] as const;
};

export const getHeartbeatQueryOptions = <
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getHeartbeatQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof heartbeat>>> = ({ signal }) =>
		heartbeat(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof heartbeat>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type HeartbeatQueryResult = NonNullable<Awaited<ReturnType<typeof heartbeat>>>;
export type HeartbeatQueryError = ErrorType<HTTPValidationError>;

export function useHeartbeat<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof heartbeat>>,
					TError,
					Awaited<ReturnType<typeof heartbeat>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useHeartbeat<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof heartbeat>>,
					TError,
					Awaited<ReturnType<typeof heartbeat>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useHeartbeat<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Heartbeat
 */

export function useHeartbeat<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getHeartbeatQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getHeartbeatSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getHeartbeatQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof heartbeat>>> = ({ signal }) =>
		heartbeat(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof heartbeat>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type HeartbeatSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof heartbeat>>>;
export type HeartbeatSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useHeartbeatSuspense<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useHeartbeatSuspense<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useHeartbeatSuspense<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Heartbeat
 */

export function useHeartbeatSuspense<
	TData = Awaited<ReturnType<typeof heartbeat>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: HeartbeatParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof heartbeat>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getHeartbeatSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns marketing info
 * @summary Get Marketing Info
 */
export const getMarketingInfo = (signal?: AbortSignal) => {
	return getAxios<MarketingInfo>({ url: `/auth/marketing_info`, method: "GET", signal });
};

export const getGetMarketingInfoQueryKey = () => {
	return [`/auth/marketing_info`] as const;
};

export const getGetMarketingInfoQueryOptions = <
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMarketingInfoQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMarketingInfo>>> = ({ signal }) =>
		getMarketingInfo(signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getMarketingInfo>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMarketingInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getMarketingInfo>>>;
export type GetMarketingInfoQueryError = ErrorType<unknown>;

export function useGetMarketingInfo<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>> &
		Pick<
			DefinedInitialDataOptions<
				Awaited<ReturnType<typeof getMarketingInfo>>,
				TError,
				Awaited<ReturnType<typeof getMarketingInfo>>
			>,
			"initialData"
		>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMarketingInfo<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>> &
		Pick<
			UndefinedInitialDataOptions<
				Awaited<ReturnType<typeof getMarketingInfo>>,
				TError,
				Awaited<ReturnType<typeof getMarketingInfo>>
			>,
			"initialData"
		>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMarketingInfo<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Marketing Info
 */

export function useGetMarketingInfo<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMarketingInfoQueryOptions(options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetMarketingInfoSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMarketingInfoQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMarketingInfo>>> = ({ signal }) =>
		getMarketingInfo(signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getMarketingInfo>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMarketingInfoSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getMarketingInfo>>
>;
export type GetMarketingInfoSuspenseQueryError = ErrorType<unknown>;

export function useGetMarketingInfoSuspense<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMarketingInfoSuspense<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMarketingInfoSuspense<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Marketing Info
 */

export function useGetMarketingInfoSuspense<
	TData = Awaited<ReturnType<typeof getMarketingInfo>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMarketingInfo>>, TError, TData>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMarketingInfoSuspenseQueryOptions(options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Update Marketing Consent
 */
export const updateMarketingConsent = (
	marketingConsentSchema: MarketingConsentSchema,
	signal?: AbortSignal
) => {
	return getAxios<OkResponse>({
		url: `/auth/update_marketing_consent`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: marketingConsentSchema,
		signal,
	});
};

export const getUpdateMarketingConsentMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateMarketingConsent>>,
		TError,
		{ data: MarketingConsentSchema },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof updateMarketingConsent>>,
	TError,
	{ data: MarketingConsentSchema },
	TContext
> => {
	const mutationKey = ["updateMarketingConsent"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof updateMarketingConsent>>,
		{ data: MarketingConsentSchema }
	> = props => {
		const { data } = props ?? {};

		return updateMarketingConsent(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type UpdateMarketingConsentMutationResult = NonNullable<
	Awaited<ReturnType<typeof updateMarketingConsent>>
>;
export type UpdateMarketingConsentMutationBody = MarketingConsentSchema;
export type UpdateMarketingConsentMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Update Marketing Consent
 */
export const useUpdateMarketingConsent = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateMarketingConsent>>,
		TError,
		{ data: MarketingConsentSchema },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof updateMarketingConsent>>,
	TError,
	{ data: MarketingConsentSchema },
	TContext
> => {
	const mutationOptions = getUpdateMarketingConsentMutationOptions(options);

	return useMutation(mutationOptions);
};
