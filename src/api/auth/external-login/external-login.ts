/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Auth API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	CreateExternalLoginData,
	ExternalLoginSchema,
	HTTPValidationError,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Create External Login
 */
export const createExternalLogin = (
	createExternalLoginData: CreateExternalLoginData,
	signal?: AbortSignal
) => {
	return getAxios<ExternalLoginSchema>({
		url: `/auth/externalLogin/create`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: createExternalLoginData,
		signal,
	});
};

export const getCreateExternalLoginMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createExternalLogin>>,
		TError,
		{ data: CreateExternalLoginData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createExternalLogin>>,
	TError,
	{ data: CreateExternalLoginData },
	TContext
> => {
	const mutationKey = ["createExternalLogin"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createExternalLogin>>,
		{ data: CreateExternalLoginData }
	> = props => {
		const { data } = props ?? {};

		return createExternalLogin(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateExternalLoginMutationResult = NonNullable<
	Awaited<ReturnType<typeof createExternalLogin>>
>;
export type CreateExternalLoginMutationBody = CreateExternalLoginData;
export type CreateExternalLoginMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create External Login
 */
export const useCreateExternalLogin = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createExternalLogin>>,
		TError,
		{ data: CreateExternalLoginData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createExternalLogin>>,
	TError,
	{ data: CreateExternalLoginData },
	TContext
> => {
	const mutationOptions = getCreateExternalLoginMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get External Login
 */
export const getExternalLogin = (uuid: string | undefined | null, signal?: AbortSignal) => {
	return getAxios<ExternalLoginSchema>({
		url: `/auth/externalLogin/${uuid}`,
		method: "GET",
		signal,
	});
};

export const getGetExternalLoginQueryKey = (uuid: string | undefined | null) => {
	return [`/auth/externalLogin/${uuid}`] as const;
};

export const getGetExternalLoginQueryOptions = <
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExternalLoginQueryKey(uuid);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExternalLogin>>> = ({ signal }) =>
		getExternalLogin(uuid, signal);

	return { queryKey, queryFn, enabled: !!uuid, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getExternalLogin>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetExternalLoginQueryResult = NonNullable<Awaited<ReturnType<typeof getExternalLogin>>>;
export type GetExternalLoginQueryError = ErrorType<HTTPValidationError>;

export function useGetExternalLogin<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getExternalLogin>>,
					TError,
					Awaited<ReturnType<typeof getExternalLogin>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetExternalLogin<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getExternalLogin>>,
					TError,
					Awaited<ReturnType<typeof getExternalLogin>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetExternalLogin<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get External Login
 */

export function useGetExternalLogin<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetExternalLoginQueryOptions(uuid, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetExternalLoginSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExternalLoginQueryKey(uuid);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExternalLogin>>> = ({ signal }) =>
		getExternalLogin(uuid, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getExternalLogin>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetExternalLoginSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getExternalLogin>>
>;
export type GetExternalLoginSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetExternalLoginSuspense<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetExternalLoginSuspense<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetExternalLoginSuspense<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get External Login
 */

export function useGetExternalLoginSuspense<
	TData = Awaited<ReturnType<typeof getExternalLogin>>,
	TError = ErrorType<HTTPValidationError>,
>(
	uuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getExternalLogin>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetExternalLoginSuspenseQueryOptions(uuid, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Cancel External Login
 */
export const cancelExternalLogin = (uuid: string | undefined | null, signal?: AbortSignal) => {
	return getAxios<ExternalLoginSchema>({
		url: `/auth/externalLogin/${uuid}/cancel`,
		method: "POST",
		signal,
	});
};

export const getCancelExternalLoginMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof cancelExternalLogin>>,
		TError,
		{ uuid: string | undefined | null },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof cancelExternalLogin>>,
	TError,
	{ uuid: string | undefined | null },
	TContext
> => {
	const mutationKey = ["cancelExternalLogin"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof cancelExternalLogin>>,
		{ uuid: string | undefined | null }
	> = props => {
		const { uuid } = props ?? {};

		return cancelExternalLogin(uuid);
	};

	return { mutationFn, ...mutationOptions };
};

export type CancelExternalLoginMutationResult = NonNullable<
	Awaited<ReturnType<typeof cancelExternalLogin>>
>;

export type CancelExternalLoginMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Cancel External Login
 */
export const useCancelExternalLogin = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof cancelExternalLogin>>,
		TError,
		{ uuid: string | undefined | null },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof cancelExternalLogin>>,
	TError,
	{ uuid: string | undefined | null },
	TContext
> => {
	const mutationOptions = getCancelExternalLoginMutationOptions(options);

	return useMutation(mutationOptions);
};
