/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc eWallet API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	EWalletPaymentData,
	EWalletPaymentResponse,
	EWalletPaymentStatusData,
	HTTPValidationError,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Create Ewallet Payment
 */
export const createEwalletPayment = (
	paymentSettingsId: number | undefined | null,
	eWalletPaymentData: EWalletPaymentData,
	signal?: AbortSignal
) => {
	return getAxios<EWalletPaymentResponse>({
		url: `/ewallet/payments/${paymentSettingsId}`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: eWalletPaymentData,
		signal,
	});
};

export const getCreateEwalletPaymentMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createEwalletPayment>>,
		TError,
		{ paymentSettingsId: number | undefined | null; data: EWalletPaymentData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createEwalletPayment>>,
	TError,
	{ paymentSettingsId: number | undefined | null; data: EWalletPaymentData },
	TContext
> => {
	const mutationKey = ["createEwalletPayment"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createEwalletPayment>>,
		{ paymentSettingsId: number | undefined | null; data: EWalletPaymentData }
	> = props => {
		const { paymentSettingsId, data } = props ?? {};

		return createEwalletPayment(paymentSettingsId, data);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateEwalletPaymentMutationResult = NonNullable<
	Awaited<ReturnType<typeof createEwalletPayment>>
>;
export type CreateEwalletPaymentMutationBody = EWalletPaymentData;
export type CreateEwalletPaymentMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create Ewallet Payment
 */
export const useCreateEwalletPayment = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createEwalletPayment>>,
		TError,
		{ paymentSettingsId: number | undefined | null; data: EWalletPaymentData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createEwalletPayment>>,
	TError,
	{ paymentSettingsId: number | undefined | null; data: EWalletPaymentData },
	TContext
> => {
	const mutationOptions = getCreateEwalletPaymentMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get Payment Status
 */
export const getPaymentStatus = (
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	signal?: AbortSignal
) => {
	return getAxios<EWalletPaymentStatusData>({
		url: `/ewallet/payments/${paymentSettingsId}/${ewalletPaymentUuid}`,
		method: "GET",
		signal,
	});
};

export const getGetPaymentStatusQueryKey = (
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null
) => {
	return [`/ewallet/payments/${paymentSettingsId}/${ewalletPaymentUuid}`] as const;
};

export const getGetPaymentStatusQueryOptions = <
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey =
		queryOptions?.queryKey ??
		getGetPaymentStatusQueryKey(paymentSettingsId, ewalletPaymentUuid);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getPaymentStatus>>> = ({ signal }) =>
		getPaymentStatus(paymentSettingsId, ewalletPaymentUuid, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(paymentSettingsId && ewalletPaymentUuid),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetPaymentStatusQueryResult = NonNullable<Awaited<ReturnType<typeof getPaymentStatus>>>;
export type GetPaymentStatusQueryError = ErrorType<HTTPValidationError>;

export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getPaymentStatus>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getPaymentStatus>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Payment Status
 */

export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetPaymentStatusQueryOptions(
		paymentSettingsId,
		ewalletPaymentUuid,
		options
	);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetPaymentStatusSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey =
		queryOptions?.queryKey ??
		getGetPaymentStatusQueryKey(paymentSettingsId, ewalletPaymentUuid);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getPaymentStatus>>> = ({ signal }) =>
		getPaymentStatus(paymentSettingsId, ewalletPaymentUuid, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getPaymentStatus>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetPaymentStatusSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getPaymentStatus>>
>;
export type GetPaymentStatusSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Payment Status
 */

export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	paymentSettingsId: number | undefined | null,
	ewalletPaymentUuid: string | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetPaymentStatusSuspenseQueryOptions(
		paymentSettingsId,
		ewalletPaymentUuid,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}
