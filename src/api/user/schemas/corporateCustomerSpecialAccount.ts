/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CorporateCustomerInfo } from "./corporateCustomerInfo";
import type { AccountLimits } from "./accountLimits";
import type { SpecialAccount } from "./specialAccount";

/**
 * Corporate customer special account info  # noqa: E501
 */
export interface CorporateCustomerSpecialAccount {
	/** Account active flag */
	active?: number;
	/** Amount of funds on the account available for using (null if unlimited) */
	available_amount?: number;
	corporate_customer?: CorporateCustomerInfo;
	/** Credit limit (for 'credit' account type) */
	credit_limit?: number;
	/** Credit type */
	credit_type?: string;
	/** Account applicable goods filter */
	goods_items?: string[];
	/** Corporate customer special account ID */
	id?: string;
	limits?: AccountLimits;
	special_account?: SpecialAccount;
}
