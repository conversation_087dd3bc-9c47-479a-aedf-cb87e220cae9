/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CorporateCustomerSpecialAccount } from "./corporateCustomerSpecialAccount";
import type { CorporateAccountAccessLimits } from "./corporateAccountAccessLimits";

/**
 * CorporateCustomerSpecialAccountAccess
 */
export interface CorporateCustomerSpecialAccountAccess {
	/** Amount of funds on the account available for using (null if unlimited) */
	available_amount?: number;
	corporate_customer_special_account?: CorporateCustomerSpecialAccount;
	/** Account applicable goods filter */
	goods_items?: string[];
	/** Access ID */
	id?: string;
	limits?: CorporateAccountAccessLimits;
	/** Odometer value needed flag */
	odometer?: string;
	/** Additional PIN-code for access to the account exists */
	security_code?: number;
	/** Vehicle ID needed flag */
	vehicle_id?: string;
}
