/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CustomerBonusesAccountActivity } from "./customerBonusesAccountActivity";

/**
 * CustomerBonusesAccount
 */
export interface CustomerBonusesAccount {
	/** Total bonuses added on account */
	bonuses_added?: number;
	/** Total activated bonuses amount on account (ready for use) */
	bonuses_amount?: number;
	/** Total bonuses redeemed from account */
	bonuses_redeemed?: number;
	/** Currensy ISO code */
	currency?: string;
	/** Currensy name */
	currency_name?: string;
	/** Total not activated yet bonuses amount on account (optional) */
	deferred_bonuses_amount?: number;
	/** Amount of not activated yet permanent bonuses on account (optional) */
	deferred_permanent_bonuses_amount?: number;
	/** Amount of not activated yet promotional bonuses on account (optional) */
	deferred_promotion_bonuses_amount?: number;
	/** Alias of deferred_promotion_bonuses_amount */
	deferred_temporary_bonuses_amount?: number;
	/** Permanent bonuses added on account */
	permanent_bonuses_added?: number;
	/** Amount of activated permanent bonuses on account (ready for use) */
	permanent_bonuses_amount?: number;
	/** Detailing of available permanent bonus points (optional) */
	permanent_bonuses_detailing?: CustomerBonusesAccountActivity[];
	/** Permanent bonuses redeemed from account */
	permanent_bonuses_redeemed?: number;
	/** Promotional bonuses added on account */
	promotion_bonuses_added?: number;
	/** Amount of activated promotional bonuses on account (ready for use) */
	promotion_bonuses_amount?: number;
	/** Detailing of available promotional bonus points (optional) */
	promotion_bonuses_detailing?: CustomerBonusesAccountActivity[];
	/** Promotional bonuses redeemed from account */
	promotion_bonuses_redeemed?: number;
	/** Alias of promotion_bonuses_amount */
	temporary_bonuses_amount?: number;
}
