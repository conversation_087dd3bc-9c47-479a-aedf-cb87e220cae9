/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { TourCardCategory } from "./tourCardCategory";

/**
 * TourCardService
 */
export interface TourCardService {
	/** Available sevice account amount */
	available_amount?: number;
	/** Service availability flag */
	available_now?: number;
	/** Number of times available for use */
	available_times?: number;
	category?: TourCardCategory;
	/** Decrement step of account amount */
	decrement_step?: number;
	/** Service ID */
	id?: string;
	/** URL of object image */
	image?: string;
	/** Public description */
	public_description?: string;
	/** Public title */
	public_title?: string;
	/** Usage limit per loyalty */
	usage_limit_per_loyalty?: number;
	/** Usage limit per loyalty period type */
	usage_limit_per_loyalty_period_type?: string;
	/** Usage limit per loyalty period value */
	usage_limit_per_loyalty_period_value?: number;
	/** Usage limit per POS */
	usage_limit_per_pos?: number;
	/** Usage limit per POS period type */
	usage_limit_per_pos_period_type?: string;
	/** Usage limit per POS period value */
	usage_limit_per_pos_period_value?: number;
	/** Number of service usage by customer (optional) */
	used_times?: number;
}
