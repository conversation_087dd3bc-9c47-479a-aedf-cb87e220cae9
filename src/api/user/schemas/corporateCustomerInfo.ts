/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CorporateCustomerInfoCorporateUser } from "./corporateCustomerInfoCorporateUser";

/**
 * Corporate customer  # noqa: E501
 */
export interface CorporateCustomerInfo {
	corporate_user?: CorporateCustomerInfoCorporateUser;
	/** Corporate customer ID */
	id?: string;
	/** Corporate customer name */
	name?: string;
	/** Customer suspended flag */
	suspended?: number;
}
