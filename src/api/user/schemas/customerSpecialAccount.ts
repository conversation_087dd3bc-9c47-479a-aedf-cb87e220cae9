/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { AccountLimits } from "./accountLimits";
import type { SpecialAccount } from "./specialAccount";

/**
 * CustomerSpecialAccount
 */
export interface CustomerSpecialAccount {
	/** On hand amount */
	amount?: number;
	/** Amount of funds on the account available for using (null if unlimited) */
	available_amount?: number;
	/** balance */
	balance?: number;
	/** Credit limit */
	credit_limit?: number;
	/** Credit_type */
	credit_type?: string;
	/** Customer special account ID */
	customer_special_account_id?: string;
	/** Account extended flag */
	extended?: number;
	/** Account applicable goods filter (optional, only for extended accounts) */
	goods_items?: string[];
	/** Special account ID */
	id?: string;
	limits?: AccountLimits;
	/** Odometer value needed flag (optional, only for extended accounts) */
	odometer?: string;
	/** Write-off from account amout (optional) */
	redeem_amount?: number;
	/** Total refill account amount (optional) */
	refill_amount?: number;
	/** Additional PIN-code for access to the account exists (optional, only for extended accounts) */
	security_code?: number;
	special_account?: SpecialAccount;
	/** Special account title */
	title?: string;
}
