/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CustomerAccessType } from "./customerAccessType";
import type { CardCategory } from "./cardCategory";
import type { UserStatus } from "./userStatus";

/**
 * Customer data  # noqa: E501
 */
export interface CustomerInfo {
	access_type?: CustomerAccessType;
	/** User avatar url */
	avatar?: string;
	card_category?: CardCategory;
	/** Created customer date and time */
	created?: string;
	/** User has mobileapp tokens (use mobileapp) */
	have_mobileapp_token?: boolean;
	/** Customer ID */
	id?: string;
	/** Customer last sale date */
	last_sale_date?: string;
	/** User is new for loyalty */
	new?: number;
	/** Show questionnaire flag */
	show_questionnaire?: number;
	/** Customer user birthdate */
	user_birth_date?: string;
	/** User consent exists flag */
	user_consent_exist?: boolean;
	/** Created user date and time */
	user_created?: string;
	user_email?: string;
	user_external_id?: string;
	user_gender?: string;
	/** Public Customer user Name */
	user_name?: string;
	user_phone?: string;
	user_status?: UserStatus;
}
