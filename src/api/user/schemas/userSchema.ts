/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { ClientEnum } from "./clientEnum";

export interface UserSchema {
	id: number;
	lang: string;
	email?: string;
	is_confirmed_email?: boolean;
	is_only_email: boolean;
	is_messanger: boolean;
	client: ClientEnum;
	chat_id?: number;
	username?: string;
	first_name?: string;
	last_name?: string;
	full_name?: string;
	wa_phone?: string;
	wa_name?: string;
	photo?: string;
	photo_url?: string;
	has_multiple_ids: boolean;
	birth_date?: string;
	name?: string;
	messangers?: unknown[];
	is_system_user: boolean;
	is_anonymous: boolean;
	is_password: boolean;
	db_timezone?: string;
	is_accepted_agreement: boolean;
	is_guest_user?: boolean;
	/** @nullable */
	incust_external_id?: string | null;
	marketing_consent?: boolean;
}
