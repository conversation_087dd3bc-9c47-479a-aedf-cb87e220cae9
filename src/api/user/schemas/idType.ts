/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */

/**
 * IdType
 */
export type IdType = (typeof IdType)[keyof typeof IdType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IdType = {
	phone: "phone",
	qr: "qr",
	email: "email",
	card: "card",
	"temporary-card": "temporary-card",
	id: "id",
	"external-id": "external-id",
	"social-network": "social-network",
	ssn: "ssn",
	itin: "itin",
	itn: "itn",
} as const;
