/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CustomerBonusesAccount } from "./customerBonusesAccount";
import type { CorporateCustomerSpecialAccountAccess } from "./corporateCustomerSpecialAccountAccess";
import type { Coupon } from "./coupon";
import type { CustomerInfo } from "./customerInfo";
import type { UserIdentifier } from "./userIdentifier";
import type { CustomerRateInfo } from "./customerRateInfo";
import type { CustomerSpecialAccount } from "./customerSpecialAccount";
import type { TourCard } from "./tourCard";

/**
 * CardInfo
 */
export interface CardInfo {
	/** Bonuses by currency ISO code list */
	bonuses?: CustomerBonusesAccount[];
	/** List of access to the corporate customer special accounts */
	corporate_special_accounts_access?: CorporateCustomerSpecialAccountAccess[];
	/** Сoupons list */
	coupons?: Coupon[];
	customer?: CustomerInfo;
	/** User identification data list */
	identification?: UserIdentifier[];
	rate_info?: CustomerRateInfo;
	/** Customer special accounts list */
	specials?: CustomerSpecialAccount[];
	/** Tourist (Prepaid) Cards list */
	tourist_cards?: TourCard[];
}
