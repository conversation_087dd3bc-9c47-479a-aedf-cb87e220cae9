/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */

/**
 * CustomerBonusesAccountActivity
 */
export interface CustomerBonusesAccountActivity {
	/** Bonuses are ready for use */
	activated?: boolean;
	/** Available bonus points amount */
	available_amount?: number;
	/** Currensy ISO code */
	currency?: string;
	/** Date of bonuses expiration */
	expired?: string;
	/** Bonus points added date and time */
	processed?: string;
	/** Date of bonuses activation (date from which bonuses are ready for use) */
	starting?: string;
	/** Unique Id of the transaction that added bonuses */
	transaction_id?: string;
}
