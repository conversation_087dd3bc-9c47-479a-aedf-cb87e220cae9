/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { CardCategoryExtendedSettings } from "./cardCategoryExtendedSettings";

/**
 * Card category  # noqa: E501
 */
export interface CardCategory {
	extended_settings?: CardCategoryExtendedSettings;
	/** Card category ID */
	id: string;
	/** URL of batch image */
	image?: string;
	/** Show category to customers */
	show_to_customers?: number;
	/** Card category title */
	title?: string;
	/** Category type (readonly) */
	type?: string;
	/** Category weight */
	weight?: number;
}
