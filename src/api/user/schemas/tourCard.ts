/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import type { TourCardAccount } from "./tourCardAccount";
import type { TourCardService } from "./tourCardService";

/**
 * TourCard
 */
export interface TourCard {
	/** Card accounts */
	accounts?: TourCardAccount[];
	/** Card activated datetime */
	activated_dt?: string;
	/** Active flag */
	active?: number;
	/** CODE */
	code?: string;
	/** Card expire datetime */
	expire_dt?: string;
	/** Card ID */
	id?: string;
	/** public description */
	public_description?: string;
	/** public title */
	public_title?: string;
	/** Card services */
	services?: TourCardService[];
	/** Card validity flag */
	valid?: number;
}
