/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */

export type GetUserAllowedActions200Item =
	(typeof GetUserAllowedActions200Item)[keyof typeof GetUserAllowedActions200Item];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetUserAllowedActions200Item = {
	"platform:read": "platform:read",
	"platform:admin": "platform:admin",
	"platform:create": "platform:create",
	"platform:superadmin": "platform:superadmin",
	"platform:edit": "platform:edit",
	"profile:admin": "profile:admin",
	"profile:create": "profile:create",
	"profile:read": "profile:read",
	"profile:edit": "profile:edit",
	"notifications:create": "notifications:create",
	"profile_data:read": "profile_data:read",
	"profile_data:edit": "profile_data:edit",
	"bot:read": "bot:read",
	"bot:edit": "bot:edit",
	"crm_all:read": "crm_all:read",
	"crm_all:edit": "crm_all:edit",
	"crm_store:read": "crm_store:read",
	"crm_store:edit": "crm_store:edit",
	"crm_order:read": "crm_order:read",
	"crm_order:edit": "crm_order:edit",
	"crm_invoice:read": "crm_invoice:read",
	"crm_invoice:edit": "crm_invoice:edit",
	"crm_ticket:read": "crm_ticket:read",
	"crm_ticket:edit": "crm_ticket:edit",
	"crm_review:read": "crm_review:read",
	"crm_review:edit": "crm_review:edit",
	"crm_chat:read": "crm_chat:read",
	"crm_chat:edit": "crm_chat:edit",
	"crm_text_notification:read": "crm_text_notification:read",
	"crm_text_notification:edit": "crm_text_notification:edit",
	"crm_user:read": "crm_user:read",
	"crm_user:edit": "crm_user:edit",
	"crm_ewallet_ext_payment:read": "crm_ewallet_ext_payment:read",
	"crm_ewallet_ext_payment:edit": "crm_ewallet_ext_payment:edit",
	"store:contribute": "store:contribute",
	"store:read": "store:read",
	"store:create": "store:create",
	"store:edit": "store:edit",
	"storage:read": "storage:read",
	"storage:edit": "storage:edit",
	"vm:contribute": "vm:contribute",
	"vm:read": "vm:read",
	"vm:create": "vm:create",
	"vm:edit": "vm:edit",
	"webhook:contribute": "webhook:contribute",
	"webhook:read": "webhook:read",
	"webhook:create": "webhook:create",
	"webhook:edit": "webhook:edit",
	"ewallet_external_payment:edit": "ewallet_external_payment:edit",
	"category:read": "category:read",
	"category:create": "category:create",
	"category:edit": "category:edit",
	"product:read": "product:read",
	"product:create": "product:create",
	"product:edit": "product:edit",
	"product_group:read": "product_group:read",
	"product_group:create": "product_group:create",
	"product_group:edit": "product_group:edit",
	"attribute_group:read": "attribute_group:read",
	"attribute_group:create": "attribute_group:create",
	"attribute_group:edit": "attribute_group:edit",
	"characteristic:read": "characteristic:read",
	"characteristic:create": "characteristic:create",
	"characteristic:edit": "characteristic:edit",
	"attribute:read": "attribute:read",
	"attribute:create": "attribute:create",
	"attribute:edit": "attribute:edit",
	"qr_menu:read": "qr_menu:read",
	"qr_menu:create": "qr_menu:create",
	"qr_menu:edit": "qr_menu:edit",
	"qr_object:read": "qr_object:read",
	"qr_object:create": "qr_object:create",
	"qr_object:edit": "qr_object:edit",
	"extra_fee:read": "extra_fee:read",
	"extra_fee:create": "extra_fee:create",
	"extra_fee:edit": "extra_fee:edit",
	"task:read": "task:read",
	"task:create": "task:create",
	"task:edit": "task:edit",
	"profile_media:read": "profile_media:read",
	"profile_media:create": "profile_media:create",
	"profile_media:edit": "profile_media:edit",
	"customer_profiles:read": "customer_profiles:read",
	"billing:tester": "billing:tester",
} as const;
