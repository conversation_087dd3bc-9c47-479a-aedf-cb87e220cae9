/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc User API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	AccessToActionsSchema,
	AddFriend200,
	ChangePasswordData,
	CheckUserAccessToActionsParams,
	DeleteUserAccountData,
	FriendData,
	GetOrSetUserLangData,
	GetUserAllowedActions200Item,
	GetUserAllowedActionsParams,
	GetUserIncustCustomerDataParams,
	HTTPValidationError,
	IsManagerOrAdmin,
	LangData,
	UnlinkMessangerData,
	UpdateUserData,
	UserAccountDeleted,
	UserIncustCustomerData,
	UserSchema,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Get User
 */
export const getUser = (signal?: AbortSignal) => {
	return getAxios<UserSchema>({ url: `/user/`, method: "GET", signal });
};

export const getGetUserQueryKey = () => {
	return [`/user/`] as const;
};

export const getGetUserQueryOptions = <
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUser>>> = ({ signal }) =>
		getUser(signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUser>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserQueryError = ErrorType<unknown>;

export function useGetUser<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>> &
		Pick<
			DefinedInitialDataOptions<
				Awaited<ReturnType<typeof getUser>>,
				TError,
				Awaited<ReturnType<typeof getUser>>
			>,
			"initialData"
		>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUser<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>> &
		Pick<
			UndefinedInitialDataOptions<
				Awaited<ReturnType<typeof getUser>>,
				TError,
				Awaited<ReturnType<typeof getUser>>
			>,
			"initialData"
		>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUser<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User
 */

export function useGetUser<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserQueryOptions(options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUser>>> = ({ signal }) =>
		getUser(signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUser>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserSuspenseQueryError = ErrorType<unknown>;

export function useGetUserSuspense<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserSuspense<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserSuspense<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User
 */

export function useGetUserSuspense<
	TData = Awaited<ReturnType<typeof getUser>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserSuspenseQueryOptions(options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Update User
 */
export const updateUser = (updateUserData: UpdateUserData) => {
	return getAxios<UserSchema>({
		url: `/user/`,
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		data: updateUserData,
	});
};

export const getUpdateUserMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateUser>>,
		TError,
		{ data: UpdateUserData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof updateUser>>,
	TError,
	{ data: UpdateUserData },
	TContext
> => {
	const mutationKey = ["updateUser"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof updateUser>>,
		{ data: UpdateUserData }
	> = props => {
		const { data } = props ?? {};

		return updateUser(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type UpdateUserMutationResult = NonNullable<Awaited<ReturnType<typeof updateUser>>>;
export type UpdateUserMutationBody = UpdateUserData;
export type UpdateUserMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Update User
 */
export const useUpdateUser = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateUser>>,
		TError,
		{ data: UpdateUserData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof updateUser>>,
	TError,
	{ data: UpdateUserData },
	TContext
> => {
	const mutationOptions = getUpdateUserMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Check User Access To Actions
 */
export const checkUserAccessToActions = (
	params: CheckUserAccessToActionsParams,
	signal?: AbortSignal
) => {
	return getAxios<AccessToActionsSchema>({
		url: `/user/check_access_to_actions`,
		method: "GET",
		params,
		signal,
	});
};

export const getCheckUserAccessToActionsQueryKey = (params: CheckUserAccessToActionsParams) => {
	return [`/user/check_access_to_actions`, ...(params ? [params] : [])] as const;
};

export const getCheckUserAccessToActionsQueryOptions = <
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkUserAccessToActions>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckUserAccessToActionsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkUserAccessToActions>>> = ({
		signal,
	}) => checkUserAccessToActions(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkUserAccessToActions>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckUserAccessToActionsQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkUserAccessToActions>>
>;
export type CheckUserAccessToActionsQueryError = ErrorType<HTTPValidationError>;

export function useCheckUserAccessToActions<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkUserAccessToActions>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkUserAccessToActions>>,
					TError,
					Awaited<ReturnType<typeof checkUserAccessToActions>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckUserAccessToActions<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkUserAccessToActions>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkUserAccessToActions>>,
					TError,
					Awaited<ReturnType<typeof checkUserAccessToActions>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckUserAccessToActions<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkUserAccessToActions>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check User Access To Actions
 */

export function useCheckUserAccessToActions<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkUserAccessToActions>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckUserAccessToActionsQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckUserAccessToActionsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof checkUserAccessToActions>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckUserAccessToActionsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkUserAccessToActions>>> = ({
		signal,
	}) => checkUserAccessToActions(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkUserAccessToActions>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckUserAccessToActionsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkUserAccessToActions>>
>;
export type CheckUserAccessToActionsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useCheckUserAccessToActionsSuspense<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof checkUserAccessToActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckUserAccessToActionsSuspense<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof checkUserAccessToActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckUserAccessToActionsSuspense<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof checkUserAccessToActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check User Access To Actions
 */

export function useCheckUserAccessToActionsSuspense<
	TData = Awaited<ReturnType<typeof checkUserAccessToActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: CheckUserAccessToActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof checkUserAccessToActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckUserAccessToActionsSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get User Incust Customer Data
 */
export const getUserIncustCustomerData = (
	params?: GetUserIncustCustomerDataParams,
	signal?: AbortSignal
) => {
	return getAxios<UserIncustCustomerData>({
		url: `/user/incustCustomerData`,
		method: "GET",
		params,
		signal,
	});
};

export const getGetUserIncustCustomerDataQueryKey = (params?: GetUserIncustCustomerDataParams) => {
	return [`/user/incustCustomerData`, ...(params ? [params] : [])] as const;
};

export const getGetUserIncustCustomerDataQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserIncustCustomerData>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserIncustCustomerDataQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserIncustCustomerData>>> = ({
		signal,
	}) => getUserIncustCustomerData(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUserIncustCustomerData>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserIncustCustomerDataQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserIncustCustomerData>>
>;
export type GetUserIncustCustomerDataQueryError = ErrorType<HTTPValidationError>;

export function useGetUserIncustCustomerData<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetUserIncustCustomerDataParams,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserIncustCustomerData>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserIncustCustomerData>>,
					TError,
					Awaited<ReturnType<typeof getUserIncustCustomerData>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserIncustCustomerData<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserIncustCustomerData>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserIncustCustomerData>>,
					TError,
					Awaited<ReturnType<typeof getUserIncustCustomerData>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserIncustCustomerData<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserIncustCustomerData>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Incust Customer Data
 */

export function useGetUserIncustCustomerData<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserIncustCustomerData>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserIncustCustomerDataQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserIncustCustomerDataSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserIncustCustomerData>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserIncustCustomerDataQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserIncustCustomerData>>> = ({
		signal,
	}) => getUserIncustCustomerData(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUserIncustCustomerData>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserIncustCustomerDataSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserIncustCustomerData>>
>;
export type GetUserIncustCustomerDataSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetUserIncustCustomerDataSuspense<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetUserIncustCustomerDataParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserIncustCustomerData>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserIncustCustomerDataSuspense<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserIncustCustomerData>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserIncustCustomerDataSuspense<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserIncustCustomerData>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Incust Customer Data
 */

export function useGetUserIncustCustomerDataSuspense<
	TData = Awaited<ReturnType<typeof getUserIncustCustomerData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserIncustCustomerDataParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserIncustCustomerData>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserIncustCustomerDataSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Or Set User Lang
 */
export const getOrSetUserLang = (
	getOrSetUserLangData: GetOrSetUserLangData,
	signal?: AbortSignal
) => {
	return getAxios<LangData>({
		url: `/user/getOrSetUserLang`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: getOrSetUserLangData,
		signal,
	});
};

export const getGetOrSetUserLangMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getOrSetUserLang>>,
		TError,
		{ data: GetOrSetUserLangData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getOrSetUserLang>>,
	TError,
	{ data: GetOrSetUserLangData },
	TContext
> => {
	const mutationKey = ["getOrSetUserLang"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getOrSetUserLang>>,
		{ data: GetOrSetUserLangData }
	> = props => {
		const { data } = props ?? {};

		return getOrSetUserLang(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetOrSetUserLangMutationResult = NonNullable<
	Awaited<ReturnType<typeof getOrSetUserLang>>
>;
export type GetOrSetUserLangMutationBody = GetOrSetUserLangData;
export type GetOrSetUserLangMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Or Set User Lang
 */
export const useGetOrSetUserLang = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getOrSetUserLang>>,
		TError,
		{ data: GetOrSetUserLangData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getOrSetUserLang>>,
	TError,
	{ data: GetOrSetUserLangData },
	TContext
> => {
	const mutationOptions = getGetOrSetUserLangMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Update User Lang
 */
export const updateUserLang = (langData: LangData) => {
	return getAxios<LangData>({
		url: `/user/lang`,
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		data: langData,
	});
};

export const getUpdateUserLangMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateUserLang>>,
		TError,
		{ data: LangData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof updateUserLang>>,
	TError,
	{ data: LangData },
	TContext
> => {
	const mutationKey = ["updateUserLang"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof updateUserLang>>,
		{ data: LangData }
	> = props => {
		const { data } = props ?? {};

		return updateUserLang(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type UpdateUserLangMutationResult = NonNullable<Awaited<ReturnType<typeof updateUserLang>>>;
export type UpdateUserLangMutationBody = LangData;
export type UpdateUserLangMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Update User Lang
 */
export const useUpdateUserLang = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateUserLang>>,
		TError,
		{ data: LangData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof updateUserLang>>,
	TError,
	{ data: LangData },
	TContext
> => {
	const mutationOptions = getUpdateUserLangMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Unlink Messanger
 */
export const unlinkMessanger = (unlinkMessangerData: UnlinkMessangerData, signal?: AbortSignal) => {
	return getAxios<UserSchema>({
		url: `/user/unlinkMessanger`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: unlinkMessangerData,
		signal,
	});
};

export const getUnlinkMessangerMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof unlinkMessanger>>,
		TError,
		{ data: UnlinkMessangerData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof unlinkMessanger>>,
	TError,
	{ data: UnlinkMessangerData },
	TContext
> => {
	const mutationKey = ["unlinkMessanger"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof unlinkMessanger>>,
		{ data: UnlinkMessangerData }
	> = props => {
		const { data } = props ?? {};

		return unlinkMessanger(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type UnlinkMessangerMutationResult = NonNullable<
	Awaited<ReturnType<typeof unlinkMessanger>>
>;
export type UnlinkMessangerMutationBody = UnlinkMessangerData;
export type UnlinkMessangerMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Unlink Messanger
 */
export const useUnlinkMessanger = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof unlinkMessanger>>,
		TError,
		{ data: UnlinkMessangerData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof unlinkMessanger>>,
	TError,
	{ data: UnlinkMessangerData },
	TContext
> => {
	const mutationOptions = getUnlinkMessangerMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Change Password
 */
export const changePassword = (changePasswordData: ChangePasswordData, signal?: AbortSignal) => {
	return getAxios<UserSchema>({
		url: `/user/changePassword`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: changePasswordData,
		signal,
	});
};

export const getChangePasswordMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof changePassword>>,
		TError,
		{ data: ChangePasswordData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof changePassword>>,
	TError,
	{ data: ChangePasswordData },
	TContext
> => {
	const mutationKey = ["changePassword"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof changePassword>>,
		{ data: ChangePasswordData }
	> = props => {
		const { data } = props ?? {};

		return changePassword(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type ChangePasswordMutationResult = NonNullable<Awaited<ReturnType<typeof changePassword>>>;
export type ChangePasswordMutationBody = ChangePasswordData;
export type ChangePasswordMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Change Password
 */
export const useChangePassword = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof changePassword>>,
		TError,
		{ data: ChangePasswordData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof changePassword>>,
	TError,
	{ data: ChangePasswordData },
	TContext
> => {
	const mutationOptions = getChangePasswordMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Check Is Manager Or Admin Exists
 */
export const checkIsManagerOrAdminExists = (signal?: AbortSignal) => {
	return getAxios<IsManagerOrAdmin>({
		url: `/user/checkIsManagerOrAdmin`,
		method: "GET",
		signal,
	});
};

export const getCheckIsManagerOrAdminExistsQueryKey = () => {
	return [`/user/checkIsManagerOrAdmin`] as const;
};

export const getCheckIsManagerOrAdminExistsQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>, TError, TData>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsManagerOrAdminExistsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>> = ({
		signal,
	}) => checkIsManagerOrAdminExists(signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsManagerOrAdminExistsQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>
>;
export type CheckIsManagerOrAdminExistsQueryError = ErrorType<unknown>;

export function useCheckIsManagerOrAdminExists<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>, TError, TData>
	> &
		Pick<
			DefinedInitialDataOptions<
				Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
				TError,
				Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>
			>,
			"initialData"
		>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsManagerOrAdminExists<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>, TError, TData>
	> &
		Pick<
			UndefinedInitialDataOptions<
				Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
				TError,
				Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>
			>,
			"initialData"
		>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsManagerOrAdminExists<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>, TError, TData>
	>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Manager Or Admin Exists
 */

export function useCheckIsManagerOrAdminExists<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseQueryOptions<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>, TError, TData>
	>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsManagerOrAdminExistsQueryOptions(options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckIsManagerOrAdminExistsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<
			Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
			TError,
			TData
		>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckIsManagerOrAdminExistsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>> = ({
		signal,
	}) => checkIsManagerOrAdminExists(signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckIsManagerOrAdminExistsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>
>;
export type CheckIsManagerOrAdminExistsSuspenseQueryError = ErrorType<unknown>;

export function useCheckIsManagerOrAdminExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<
		UseSuspenseQueryOptions<
			Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
			TError,
			TData
		>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsManagerOrAdminExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<
			Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
			TError,
			TData
		>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckIsManagerOrAdminExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<
			Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
			TError,
			TData
		>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Is Manager Or Admin Exists
 */

export function useCheckIsManagerOrAdminExistsSuspense<
	TData = Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<
		UseSuspenseQueryOptions<
			Awaited<ReturnType<typeof checkIsManagerOrAdminExists>>,
			TError,
			TData
		>
	>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckIsManagerOrAdminExistsSuspenseQueryOptions(options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Delete Friend
 */
export const deleteFriend = (friendId: number | undefined | null) => {
	return getAxios<FriendData[]>({ url: `/user/friends/${friendId}`, method: "DELETE" });
};

export const getDeleteFriendMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteFriend>>,
		TError,
		{ friendId: number | undefined | null },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof deleteFriend>>,
	TError,
	{ friendId: number | undefined | null },
	TContext
> => {
	const mutationKey = ["deleteFriend"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteFriend>>,
		{ friendId: number | undefined | null }
	> = props => {
		const { friendId } = props ?? {};

		return deleteFriend(friendId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteFriendMutationResult = NonNullable<Awaited<ReturnType<typeof deleteFriend>>>;

export type DeleteFriendMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Delete Friend
 */
export const useDeleteFriend = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteFriend>>,
		TError,
		{ friendId: number | undefined | null },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof deleteFriend>>,
	TError,
	{ friendId: number | undefined | null },
	TContext
> => {
	const mutationOptions = getDeleteFriendMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get Friends
 */
export const getFriends = (signal?: AbortSignal) => {
	return getAxios<FriendData[]>({ url: `/user/friends`, method: "GET", signal });
};

export const getGetFriendsQueryKey = () => {
	return [`/user/friends`] as const;
};

export const getGetFriendsQueryOptions = <
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFriendsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFriends>>> = ({ signal }) =>
		getFriends(signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getFriends>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFriendsQueryResult = NonNullable<Awaited<ReturnType<typeof getFriends>>>;
export type GetFriendsQueryError = ErrorType<unknown>;

export function useGetFriends<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>> &
		Pick<
			DefinedInitialDataOptions<
				Awaited<ReturnType<typeof getFriends>>,
				TError,
				Awaited<ReturnType<typeof getFriends>>
			>,
			"initialData"
		>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriends<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>> &
		Pick<
			UndefinedInitialDataOptions<
				Awaited<ReturnType<typeof getFriends>>,
				TError,
				Awaited<ReturnType<typeof getFriends>>
			>,
			"initialData"
		>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriends<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Friends
 */

export function useGetFriends<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFriendsQueryOptions(options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetFriendsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFriendsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFriends>>> = ({ signal }) =>
		getFriends(signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getFriends>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFriendsSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getFriends>>>;
export type GetFriendsSuspenseQueryError = ErrorType<unknown>;

export function useGetFriendsSuspense<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options: {
	query: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriendsSuspense<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriendsSuspense<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Friends
 */

export function useGetFriendsSuspense<
	TData = Awaited<ReturnType<typeof getFriends>>,
	TError = ErrorType<unknown>,
>(options?: {
	query?: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriends>>, TError, TData>>;
}): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFriendsSuspenseQueryOptions(options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Friend
 */
export const getFriend = (reqUserId: number | undefined | null, signal?: AbortSignal) => {
	return getAxios<UserSchema>({ url: `/user/friends/${reqUserId}`, method: "GET", signal });
};

export const getGetFriendQueryKey = (reqUserId: number | undefined | null) => {
	return [`/user/friends/${reqUserId}`] as const;
};

export const getGetFriendQueryOptions = <
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFriendQueryKey(reqUserId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFriend>>> = ({ signal }) =>
		getFriend(reqUserId, signal);

	return { queryKey, queryFn, enabled: !!reqUserId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getFriend>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFriendQueryResult = NonNullable<Awaited<ReturnType<typeof getFriend>>>;
export type GetFriendQueryError = ErrorType<HTTPValidationError>;

export function useGetFriend<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFriend>>,
					TError,
					Awaited<ReturnType<typeof getFriend>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriend<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFriend>>,
					TError,
					Awaited<ReturnType<typeof getFriend>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriend<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Friend
 */

export function useGetFriend<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFriendQueryOptions(reqUserId, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetFriendSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFriendQueryKey(reqUserId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFriend>>> = ({ signal }) =>
		getFriend(reqUserId, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getFriend>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFriendSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getFriend>>>;
export type GetFriendSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetFriendSuspense<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriendSuspense<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFriendSuspense<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Friend
 */

export function useGetFriendSuspense<
	TData = Awaited<ReturnType<typeof getFriend>>,
	TError = ErrorType<HTTPValidationError>,
>(
	reqUserId: number | undefined | null,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFriend>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFriendSuspenseQueryOptions(reqUserId, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Add Friend
 */
export const addFriend = (reqUserId: number | undefined | null, signal?: AbortSignal) => {
	return getAxios<AddFriend200>({ url: `/user/friends/${reqUserId}`, method: "POST", signal });
};

export const getAddFriendMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addFriend>>,
		TError,
		{ reqUserId: number | undefined | null },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof addFriend>>,
	TError,
	{ reqUserId: number | undefined | null },
	TContext
> => {
	const mutationKey = ["addFriend"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof addFriend>>,
		{ reqUserId: number | undefined | null }
	> = props => {
		const { reqUserId } = props ?? {};

		return addFriend(reqUserId);
	};

	return { mutationFn, ...mutationOptions };
};

export type AddFriendMutationResult = NonNullable<Awaited<ReturnType<typeof addFriend>>>;

export type AddFriendMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Add Friend
 */
export const useAddFriend = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addFriend>>,
		TError,
		{ reqUserId: number | undefined | null },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof addFriend>>,
	TError,
	{ reqUserId: number | undefined | null },
	TContext
> => {
	const mutationOptions = getAddFriendMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Delete User Account
 */
export const deleteUserAccount = (
	deleteUserAccountData: DeleteUserAccountData,
	signal?: AbortSignal
) => {
	return getAxios<UserAccountDeleted>({
		url: `/user/delete-account`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: deleteUserAccountData,
		signal,
	});
};

export const getDeleteUserAccountMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteUserAccount>>,
		TError,
		{ data: DeleteUserAccountData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof deleteUserAccount>>,
	TError,
	{ data: DeleteUserAccountData },
	TContext
> => {
	const mutationKey = ["deleteUserAccount"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteUserAccount>>,
		{ data: DeleteUserAccountData }
	> = props => {
		const { data } = props ?? {};

		return deleteUserAccount(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteUserAccountMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteUserAccount>>
>;
export type DeleteUserAccountMutationBody = DeleteUserAccountData;
export type DeleteUserAccountMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Delete User Account
 */
export const useDeleteUserAccount = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteUserAccount>>,
		TError,
		{ data: DeleteUserAccountData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof deleteUserAccount>>,
	TError,
	{ data: DeleteUserAccountData },
	TContext
> => {
	const mutationOptions = getDeleteUserAccountMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get User Allowed Actions
 */
export const getUserAllowedActions = (
	params?: GetUserAllowedActionsParams,
	signal?: AbortSignal
) => {
	return getAxios<GetUserAllowedActions200Item[]>({
		url: `/user/allowed_actions`,
		method: "GET",
		params,
		signal,
	});
};

export const getGetUserAllowedActionsQueryKey = (params?: GetUserAllowedActionsParams) => {
	return [`/user/allowed_actions`, ...(params ? [params] : [])] as const;
};

export const getGetUserAllowedActionsQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserAllowedActions>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserAllowedActionsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserAllowedActions>>> = ({
		signal,
	}) => getUserAllowedActions(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUserAllowedActions>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserAllowedActionsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserAllowedActions>>
>;
export type GetUserAllowedActionsQueryError = ErrorType<HTTPValidationError>;

export function useGetUserAllowedActions<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetUserAllowedActionsParams,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserAllowedActions>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserAllowedActions>>,
					TError,
					Awaited<ReturnType<typeof getUserAllowedActions>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserAllowedActions<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserAllowedActions>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserAllowedActions>>,
					TError,
					Awaited<ReturnType<typeof getUserAllowedActions>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserAllowedActions<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserAllowedActions>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Allowed Actions
 */

export function useGetUserAllowedActions<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserAllowedActions>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserAllowedActionsQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserAllowedActionsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserAllowedActions>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserAllowedActionsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserAllowedActions>>> = ({
		signal,
	}) => getUserAllowedActions(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUserAllowedActions>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserAllowedActionsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserAllowedActions>>
>;
export type GetUserAllowedActionsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetUserAllowedActionsSuspense<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetUserAllowedActionsParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserAllowedActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserAllowedActionsSuspense<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserAllowedActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserAllowedActionsSuspense<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserAllowedActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Allowed Actions
 */

export function useGetUserAllowedActionsSuspense<
	TData = Awaited<ReturnType<typeof getUserAllowedActions>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetUserAllowedActionsParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getUserAllowedActions>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserAllowedActionsSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}
