/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { PriceCorporateUser } from "./priceCorporateUser";

/**
 * PriceCorporateCustomer
 */
export interface PriceCorporateCustomer {
	corporate_user?: PriceCorporateUser;
	/** Customer id */
	id?: string;
	/** Customer name */
	name?: string;
	/** Customer suspended flag */
	suspended?: number;
}
