/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * IdType
 */
export type IncustTerminalApiClientModelsIdTypeIdType =
	(typeof IncustTerminalApiClientModelsIdTypeIdType)[keyof typeof IncustTerminalApiClientModelsIdTypeIdType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustTerminalApiClientModelsIdTypeIdType = {
	phone: "phone",
	qr: "qr",
	email: "email",
	card: "card",
	"temporary-card": "temporary-card",
	id: "id",
	"external-id": "external-id",
	"social-network": "social-network",
	ssn: "ssn",
	itin: "itin",
	itn: "itn",
} as const;
