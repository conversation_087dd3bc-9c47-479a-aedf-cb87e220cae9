/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * TankConfiguration
 */
export interface IncustClientApiClientModelsTankConfigurationTankConfiguration {
	/** Critical high product height alarm, mm */
	critical_high_product_alarm_height?: number;
	/** Critical low product height alarm, mm */
	critical_low_product_alarm_height?: number;
	/** Height, mm */
	height?: number;
	/** High product height alarm, mm */
	high_product_alarm_height?: number;
	/** High water alarm height, mm */
	high_water_alarm_height?: number;
	/** Low product height alarm, mm */
	low_product_alarm_height?: number;
}
