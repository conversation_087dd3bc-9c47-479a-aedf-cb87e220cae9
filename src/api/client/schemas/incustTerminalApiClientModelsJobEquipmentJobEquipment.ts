/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsPriceMarginsPriceMargins } from "./incustTerminalApiClientModelsPriceMarginsPriceMargins";
import type { IncustTerminalApiClientModelsPriceTaxesPriceTaxes } from "./incustTerminalApiClientModelsPriceTaxesPriceTaxes";
import type { IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType } from "./incustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType";

/**
 * JobEquipment
 */
export interface IncustTerminalApiClientModelsJobEquipmentJobEquipment {
	/** Authorized item amount */
	authorized_amount?: number;
	/** Authorized item price */
	authorized_price?: number;
	/** Authorized price currency ISO code (Only if filling_price_type is 'currency') */
	authorized_price_currency?: string;
	authorized_price_margins?: IncustTerminalApiClientModelsPriceMarginsPriceMargins;
	/** Authorized price special account Id (Only if filling_price_type is 'special-account') */
	authorized_price_special_account_id?: string;
	authorized_price_taxes?: IncustTerminalApiClientModelsPriceTaxesPriceTaxes;
	/** Authorized price type */
	authorized_price_type?: string;
	/** Authorized price unit - quantity or currency ISO code (Only if filling_price_type is 'special-account') */
	authorized_price_unit?: string;
	/** Authorized item quantity */
	authorized_quantity?: number;
	/** AVI (automatic vehicle identification) Id value (optional) */
	avi_id?: string;
	/** Item amount captured by check */
	captured_amount?: number;
	/** Item price captured by check */
	captured_price?: number;
	/** Item quantity captured by check */
	captured_quantity?: number;
	/** Charged item amount */
	charged_amount?: number;
	/** Charged item price */
	charged_price?: number;
	/** Charged item quantity */
	charged_quantity?: number;
	/** Charging item amount */
	charging_amount?: number;
	/** Charging item price */
	charging_price?: number;
	/** Charging item quantity */
	charging_quantity?: number;
	/** Controller connection failed flag (Exists and set to true only if connection with controller failed) */
	connection_failed?: boolean;
	/** Controller Id */
	controller_id?: string;
	/** Controller type */
	controller_type?: string;
	/** Equipment Id */
	equipment_id: string;
	/** Controller input number */
	equipment_input_number?: number;
	/** Ability to monitor the working condition flag */
	equipment_monitor_working_status?: number;
	/** Controller output number */
	equipment_output_number?: number;
	/** Equipment type */
	equipment_type?: string;
	/** Error message (Exists only if controller send error message) */
	error_message?: string;
	/** Goods code */
	goods_code?: string;
	/** Item amount */
	item_amount?: number;
	/** Item price */
	item_price?: number;
	/** Item quantity */
	item_quantity?: number;
	/** Odometer/engine hours value, up to two decimal digits (optional) */
	odometer?: number;
	odometer_unit?: IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType;
	/** Equipment charging status */
	status?: string;
	/** Qr Code / Tag of vehicle or equipment value (optional) */
	vehicle_id?: string;
}
