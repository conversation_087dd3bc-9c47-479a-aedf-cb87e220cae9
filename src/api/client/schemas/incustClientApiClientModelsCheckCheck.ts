/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsBillingInfoBillingInfo } from "./incustClientApiClientModelsBillingInfoBillingInfo";
import type { IncustClientApiClientModelsBonusAddedBonusAdded } from "./incustClientApiClientModelsBonusAddedBonusAdded";
import type { IncustClientApiClientModelsCheckBusinessCheckBusiness } from "./incustClientApiClientModelsCheckBusinessCheckBusiness";
import type { IncustClientApiClientModelsCheckMarginsCheckMargins } from "./incustClientApiClientModelsCheckMarginsCheckMargins";
import type { IncustClientApiClientModelsCheckTaxesCheckTaxes } from "./incustClientApiClientModelsCheckTaxesCheckTaxes";
import type { IncustClientApiClientModelsCheckItemCheckItem } from "./incustClientApiClientModelsCheckItemCheckItem";
import type { IncustClientApiClientModelsCheckServiceCheckService } from "./incustClientApiClientModelsCheckServiceCheckService";
import type { IncustClientApiClientModelsCorporateVehicleInfoCorporateVehicleInfo } from "./incustClientApiClientModelsCorporateVehicleInfoCorporateVehicleInfo";
import type { CheckCustomerFeedbackSettings } from "./checkCustomerFeedbackSettings";
import type { IncustClientApiClientModelsCouponCoupon } from "./incustClientApiClientModelsCouponCoupon";
import type { IncustClientApiClientModelsCheckCheckExternalAdditionalInfo } from "./incustClientApiClientModelsCheckCheckExternalAdditionalInfo";
import type { IncustClientApiClientModelsIdTypeIdType } from "./incustClientApiClientModelsIdTypeIdType";
import type { IncustClientApiClientModelsCheckCheckImplementedRulesItem } from "./incustClientApiClientModelsCheckCheckImplementedRulesItem";
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";
import type { IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType } from "./incustClientApiClientModelsOdometerUnitTypeOdometerUnitType";
import type { PaymentInfo } from "./paymentInfo";
import type { IncustClientApiClientModelsPosPos } from "./incustClientApiClientModelsPosPos";
import type { IncustClientApiClientModelsTransactionTypeTransactionType } from "./incustClientApiClientModelsTransactionTypeTransactionType";
import type { IncustClientApiClientModelsSpecialAccountSpecialAccount } from "./incustClientApiClientModelsSpecialAccountSpecialAccount";
import type { IncustClientApiClientModelsSpecialAccountChargeSpecialAccountCharge } from "./incustClientApiClientModelsSpecialAccountChargeSpecialAccountCharge";
import type { IncustClientApiClientModelsCheckTankCheckTank } from "./incustClientApiClientModelsCheckTankCheckTank";
import type { IncustClientApiClientModelsTransactionReceiptActionsTransactionReceiptActions } from "./incustClientApiClientModelsTransactionReceiptActionsTransactionReceiptActions";
import type { IncustClientApiClientModelsCheckUserExternalFormRelationsCheckUserExternalFormRelations } from "./incustClientApiClientModelsCheckUserExternalFormRelationsCheckUserExternalFormRelations";

/**
 * Check
 */
export interface IncustClientApiClientModelsCheckCheck {
	/** Amount of check */
	amount: number;
	/** Number of decimal digits in amount value */
	amount_decimal_digits?: number;
	/** Amount to pay */
	amount_to_pay?: number;
	/** AVI (automatic vehicle identification) Id value (optional) */
	avi_id?: string;
	billing_info?: IncustClientApiClientModelsBillingInfoBillingInfo;
	bonuses_added?: IncustClientApiClientModelsBonusAddedBonusAdded[];
	/** Added bonuses amount */
	bonuses_added_amount?: number;
	/** Number of decimal digits in bonuses value */
	bonuses_decimal_digits?: number;
	/** Redeemed bonuses amount */
	bonuses_redeemed_amount?: number;
	business?: IncustClientApiClientModelsCheckBusinessCheckBusiness;
	calculated_margins?: IncustClientApiClientModelsCheckMarginsCheckMargins;
	calculated_taxes?: IncustClientApiClientModelsCheckTaxesCheckTaxes;
	check_items?: IncustClientApiClientModelsCheckItemCheckItem[];
	/** Services added on check (optional) */
	check_services?: IncustClientApiClientModelsCheckServiceCheckService[];
	/** Comment */
	comment?: string;
	/** Corporate Customer ID (optional) */
	corporate_customer_id?: string;
	/** Corporate Customer Account ID */
	corporate_customer_special_account_id?: string;
	corporate_vehicle?: IncustClientApiClientModelsCorporateVehicleInfoCorporateVehicleInfo;
	customer_feedback_settings?: CheckCustomerFeedbackSettings;
	/** Customer user ID (optional) */
	customer_id?: string;
	/** Amount of discount */
	discount_amount?: number;
	emitted_coupons?: IncustClientApiClientModelsCouponCoupon[];
	/** Any custom external data (optional) */
	external_additional_info?: IncustClientApiClientModelsCheckCheckExternalAdditionalInfo;
	/** External check identifier */
	external_check_id?: string;
	/** External check identifier scope */
	external_check_id_scope?: string;
	/** User identifier (phone number, card code, QR code). Minimum 3 chars (optional) */
	id?: string;
	id_type?: IncustClientApiClientModelsIdTypeIdType;
	implemented_rules?: IncustClientApiClientModelsCheckCheckImplementedRulesItem[];
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	/** Odometer/engine hours value, up to two decimal digits (optional) */
	odometer?: number;
	odometer_unit?: IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType;
	/** Online store order id */
	online_store_order_id?: string;
	/** Currency ISO code, special account Id or corporate special account access */
	payment_id?: string;
	/** Payment type */
	payment_type?: string;
	payments?: PaymentInfo[];
	pos?: IncustClientApiClientModelsPosPos;
	/** Preliminary Transaction Id (optional, only if exists) */
	preliminary_transaction_id?: string;
	preliminary_transaction_type?: IncustClientApiClientModelsTransactionTypeTransactionType;
	redeemed_coupons?: IncustClientApiClientModelsCouponCoupon[];
	/** Route operation Id */
	route_operation_id?: string;
	/** Added on check services total amount (optional) */
	services_amount?: number;
	/** Shipping amount (optional) */
	shipping_amount?: number;
	/** Skip notifications */
	skip_message?: boolean;
	special_account?: IncustClientApiClientModelsSpecialAccountSpecialAccount;
	/** Odometer value required flag for operating with special account (response only) */
	special_account_odometer_required?: string;
	/** PIN code for operating with special account (request only) */
	special_account_pin?: string;
	/** PIN required flag for operating with special account (response only) */
	special_account_pin_required?: boolean;
	/** Verify special account required values flag (only for process_check, ignored in othe methods) */
	special_account_required_values_verify?: boolean;
	/** VIN (other vehicle identifier) value required flag for operating with special account (response only) */
	special_account_vehicle_id_required?: string;
	special_accounts_charges?: IncustClientApiClientModelsSpecialAccountChargeSpecialAccountCharge[];
	/** Number of decimal digits in special account value */
	special_accounts_decimal_digits?: number;
	/** Sum of amount and shipping_amount and services_amount (optional, readonly) */
	summary_amount?: number;
	/** Tanks Data (optional) */
	tanks?: IncustClientApiClientModelsCheckTankCheckTank[];
	terminal_odometer_measurement_units?: IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType;
	/** Transaction canceled flag */
	transaction_cancelled?: number;
	/** Transaction ID */
	transaction_id?: string;
	transaction_receipt_actions?: IncustClientApiClientModelsTransactionReceiptActionsTransactionReceiptActions;
	transaction_subtype?: IncustClientApiClientModelsTransactionTypeTransactionType;
	transaction_type?: IncustClientApiClientModelsTransactionTypeTransactionType;
	user_external_forms?: IncustClientApiClientModelsCheckUserExternalFormRelationsCheckUserExternalFormRelations;
	/** Qr Code / Tag of vehicle or equipment value (optional) */
	vehicle_id?: string;
}
