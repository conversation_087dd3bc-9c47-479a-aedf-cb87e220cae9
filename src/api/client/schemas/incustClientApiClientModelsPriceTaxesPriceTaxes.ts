/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem } from "./incustClientApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem";

/**
 * PriceTaxes
 */
export interface IncustClientApiClientModelsPriceTaxesPriceTaxes {
	/** Base taxes detailing */
	base?: IncustClientApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem[];
	/** Sales taxes detailing */
	sales?: IncustClientApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem[];
}
