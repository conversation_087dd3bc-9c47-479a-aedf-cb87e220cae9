/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { ImageObject } from "./imageObject";
import type { RefferalProgramRewardCouponBatch } from "./refferalProgramRewardCouponBatch";
import type { RefferalProgramRewardSpecialAccount } from "./refferalProgramRewardSpecialAccount";
import type { RefferalProgramLevel } from "./refferalProgramLevel";

/**
 * ReferralProgram
 */
export interface ReferralProgram {
	/** Referral program active flag */
	active?: number;
	/** Description (optional) */
	description?: string;
	/** Referral program public description for referral (optional) */
	referral_description?: string;
	referral_logo?: ImageObject;
	/** Bonuses currency (optional) */
	referral_reward_bonuses_currency?: string;
	referral_reward_coupon_batch?: RefferalProgramRewardCouponBatch;
	/** Value of promotional bonuses expire date (optional) */
	referral_reward_promotional_bonuses_expire_date?: string;
	/** Promotional bonuses expiration value type (optional) */
	referral_reward_promotional_bonuses_expire_type?: string;
	/** Value of promotional bonuses expire (optional) */
	referral_reward_promotional_bonuses_expire_value?: number;
	/** Value of promotional bonuses start date (optional) */
	referral_reward_promotional_bonuses_starting_date?: string;
	/** Promotional bonuses starting value type (optional) */
	referral_reward_promotional_bonuses_starting_type?: string;
	/** Value of promotional bonuses start (optional) */
	referral_reward_promotional_bonuses_starting_value?: number;
	referral_reward_special_account?: RefferalProgramRewardSpecialAccount;
	/** Referral reward type (optional) */
	referral_reward_type?: string;
	/** Referral reward value (optional) */
	referral_reward_value?: number;
	/** Referral program public title for referral (optional) */
	referral_title?: string;
	/** Referral program public description for referrer (optional) */
	referrer_description?: string;
	referrer_logo?: ImageObject;
	/** Value of the number of checks that will be rewarded (optional) */
	referrer_reward_grant_checks_number?: number;
	/** Type of time period that will be rewarder (optional) */
	referrer_reward_grant_period_type?: string;
	/** Value of time period that will be rewarder (optional) */
	referrer_reward_grant_period_value?: number;
	/** Referral reward grant type (optional) */
	referrer_reward_grant_type?: string;
	/** Levels of referral program (optional) */
	referrer_reward_levels?: RefferalProgramLevel[];
	/** Value of promotional bonuses expire date (optional) */
	referrer_reward_promotional_bonuses_expire_date?: string;
	/** Promotional bonuses expiration value type (optional) */
	referrer_reward_promotional_bonuses_expire_type?: string;
	/** Value of promotional bonuses expire (optional) */
	referrer_reward_promotional_bonuses_expire_value?: number;
	/** Value of promotional bonuses start date (optional) */
	referrer_reward_promotional_bonuses_starting_date?: string;
	/** Promotional bonuses starting value type (optional) */
	referrer_reward_promotional_bonuses_starting_type?: string;
	/** Value of promotional bonuses start (optional) */
	referrer_reward_promotional_bonuses_starting_value?: number;
	referrer_reward_special_account?: RefferalProgramRewardSpecialAccount;
	/** Referral reward type (optional) */
	referrer_reward_type?: string;
	/** Referrer reward value (optional) */
	referrer_reward_value?: number;
	/** Referral reward value type (optional) */
	referrer_reward_value_type?: string;
	/** Referral program public title for referrer (optional) */
	referrer_title?: string;
	/** Title (optional) */
	title?: string;
}
