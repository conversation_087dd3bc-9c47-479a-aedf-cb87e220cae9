/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCorporateStaffPositionCorporateStaffPosition } from "./incustClientApiClientModelsCorporateStaffPositionCorporateStaffPosition";

/**
 * CorporateVehicleInfo
 */
export interface IncustClientApiClientModelsCorporateVehicleInfoCorporateVehicleInfo {
	/** Vehicle/Inventory AVI (automatic vehicle identification) Id (optional) */
	avi_id?: string;
	/** Description */
	description?: string;
	/** Vehicle/Inventory unique Id */
	id?: string;
	position?: IncustClientApiClientModelsCorporateStaffPositionCorporateStaffPosition;
	/** Position ID */
	position_id?: string;
	/** Qr Code / Tag */
	qr_code?: string;
	/** Vehicle/Inventory Id */
	vehicle_id?: string;
}
