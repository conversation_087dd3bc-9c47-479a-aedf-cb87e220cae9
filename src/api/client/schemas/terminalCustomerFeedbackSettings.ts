/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsFeedbackSourceFeedbackSource } from "./incustClientApiClientModelsFeedbackSourceFeedbackSource";

/**
 * TerminalCustomerFeedbackSettings
 */
export interface TerminalCustomerFeedbackSettings {
	/** Feedback system active flag */
	active?: number;
	feedback_source?: IncustClientApiClientModelsFeedbackSourceFeedbackSource;
	/** Print feedback source on check */
	print_feedback_on_check?: string;
	/** Invitation message text for printinting on check */
	print_feedback_on_check_message_text?: string;
	/** Send invitation to ratings/reviews in mobile app when finalioze transaction */
	transaction_feedback_message_send?: number;
}
