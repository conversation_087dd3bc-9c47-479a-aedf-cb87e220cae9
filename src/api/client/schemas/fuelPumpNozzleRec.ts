/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { Goods } from "./goods";

/**
 * FuelPumpNozzleRec
 */
export interface FuelPumpNozzleRec {
	/** Nozzle fuel grade code */
	code?: string;
	goods?: Goods;
	/** Nozzle ID */
	id?: string;
	/** Internal nozzle ID / Nozzle number */
	internal_id?: string;
	/** Tank Id (optional) */
	tank_id?: string;
	/** Nozzle taken up */
	up?: boolean;
}
