/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * MobileAppPaymentSystemType
 */
export type MobileAppPaymentSystemType =
	(typeof MobileAppPaymentSystemType)[keyof typeof MobileAppPaymentSystemType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const MobileAppPaymentSystemType = {
	ipay: "ipay",
	stripe: "stripe",
	"4bill": "4bill",
	eway: "eway",
	flutterwave: "flutterwave",
	"global-payments": "global-payments",
	wave: "wave",
} as const;
