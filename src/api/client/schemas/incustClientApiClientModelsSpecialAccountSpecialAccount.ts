/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * SpecialAccount
 */
export interface IncustClientApiClientModelsSpecialAccountSpecialAccount {
	/** Active flag (optional) */
	active?: number;
	/** Corporate accoiunt flag (optional) */
	corporate?: number;
	/** Credit limit (only for 'credit' accounts) (optional) */
	credit_limit?: number;
	/** Credit type (only for corporate accounts) (optional) */
	credit_type?: string;
	/** Currency ISO code */
	currency?: string;
	/** Account applicable goods (optional) */
	goods_items?: string[];
	/** Field id */
	id?: string;
	/** Title to show (optional) */
	public_title?: string;
	/** Account title */
	title?: string;
	/** Account type */
	type?: string;
	/** The limit of redemption from customer account without an confirmation (optional) */
	unconfirmed_redeem_amount?: number;
}
