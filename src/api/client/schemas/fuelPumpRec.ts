/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { FuelPumpNozzleRec } from "./fuelPumpNozzleRec";

/**
 * FuelPumpRec
 */
export interface FuelPumpRec {
	/** Is available available for operations */
	available?: boolean;
	/** Pump ID */
	id?: string;
	/** Internal pump ID / Pump number */
	internal_id?: string;
	/** Nozzles info */
	nozzles?: FuelPumpNozzleRec[];
	/** Pump status */
	status?: string;
	/** Pump status description */
	status_description?: string;
	/** Linked terminal ID */
	terminal_id?: string;
}
