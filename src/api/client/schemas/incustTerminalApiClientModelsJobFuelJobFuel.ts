/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsFuelControllerConfigurationFuelControllerConfiguration } from "./incustTerminalApiClientModelsFuelControllerConfigurationFuelControllerConfiguration";
import type { IncustTerminalApiClientModelsPriceMarginsPriceMargins } from "./incustTerminalApiClientModelsPriceMarginsPriceMargins";
import type { IncustTerminalApiClientModelsPriceTaxesPriceTaxes } from "./incustTerminalApiClientModelsPriceTaxesPriceTaxes";
import type { IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType } from "./incustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType";
import type { IncustTerminalApiClientModelsFuelTankFuelTank } from "./incustTerminalApiClientModelsFuelTankFuelTank";

/**
 * JobFuel
 */
export interface IncustTerminalApiClientModelsJobFuelJobFuel {
	/** AVI (automatic vehicle identification) Id value (optional) */
	avi_id?: string;
	/** Fuel amount captured by check */
	captured_amount?: number;
	/** Fuel price captured by check */
	captured_price?: number;
	/** Fuel volume captured by check */
	captured_volume?: number;
	/** Controller connection failed flag (Exists and set to true only if connection with controller failed) */
	connection_failed?: boolean;
	/** Internal controller ID */
	controller?: string;
	controller_configuration?: IncustTerminalApiClientModelsFuelControllerConfigurationFuelControllerConfiguration;
	/** Controller Id */
	controller_id?: string;
	/** Controller type */
	controller_type?: string;
	/** Error message (Exists only if controller send error message) */
	error_message?: string;
	/** Job money amount */
	filled_amount?: number;
	/** Job filled volume */
	filled_volume?: number;
	/** Filling preset dose in volume units (for preset type Volume) or in currency units (for preset type Amount) */
	filling_doze?: number;
	/** Filling fuel price */
	filling_price?: number;
	/** Filling price currency ISO code (Only if filling_price_type is 'currency') */
	filling_price_currency?: string;
	filling_price_margins?: IncustTerminalApiClientModelsPriceMarginsPriceMargins;
	/** Filling price special account Id (Only if filling_price_type is 'special-account') */
	filling_price_special_account_id?: string;
	filling_price_taxes?: IncustTerminalApiClientModelsPriceTaxesPriceTaxes;
	/** Filling price type */
	filling_price_type?: string;
	/** Filling price unit - quantity or currency ISO code (Only if filling_price_type is 'special-account') */
	filling_price_unit?: string;
	/** Filling preset type */
	filling_type?: string;
	/** Goods code */
	goods_code?: string;
	/** Internal grade ID / Grade number */
	grade?: string;
	/** Grade Id */
	grade_id?: string;
	/** Internal nozzle ID / Pump nozzle */
	nozzle?: string;
	/** Nozzle Id */
	nozzle_id: string;
	/** Odometer/engine hours value, up to two decimal digits (optional) */
	odometer?: number;
	odometer_unit?: IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType;
	/** Internal pump ID / Pump number */
	pump?: string;
	/** Pump authorize confirmation type */
	pump_authorize_confirmation?: string;
	/** Pump Id */
	pump_id?: string;
	/** Fuel filling status */
	status?: string;
	/** Internal tank ID / Tank number */
	tank?: string;
	tank_details?: IncustTerminalApiClientModelsFuelTankFuelTank;
	/** Tank Id */
	tank_id?: string;
	/** Qr Code / Tag of vehicle or equipment value (optional) */
	vehicle_id?: string;
}
