/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";

/**
 * GiftCard
 */
export interface GiftCard {
	/** Business code */
	business_code?: string;
	/** Gift Card code */
	code?: string;
	/** Gift Card Description */
	description?: string;
	/** Unique id of Gift Card */
	id?: string;
	/** URL of Gift Card image (optional) */
	image?: string;
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	/** Loyalty emission id */
	loyalty_id?: string;
	/** Gift Card Title */
	title?: string;
}
