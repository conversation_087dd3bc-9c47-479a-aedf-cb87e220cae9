/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { RuleCalculationBaseItemData } from "./ruleCalculationBaseItemData";

/**
 * RuleImplementationResultItemData
 */
export interface RuleImplementationResultItemData {
	/** Rule amount for item row */
	amount?: number;
	calculation_base?: RuleCalculationBaseItemData;
	/** Rule amount for item unit row (optional) */
	unit_amount?: number;
}
