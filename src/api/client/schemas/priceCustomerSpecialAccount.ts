/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { PriceCorporateCustomer } from "./priceCorporateCustomer";
import type { IncustClientApiClientModelsAccountLimitsAccountLimits } from "./incustClientApiClientModelsAccountLimitsAccountLimits";
import type { PriceRetailCustomer } from "./priceRetailCustomer";

/**
 * PriceCustomerSpecialAccount
 */
export interface PriceCustomerSpecialAccount {
	/** Active flag */
	active?: number;
	/** Amount of funds in the account available for using (null if unlimited)(optional, for extended accounts info only) */
	available_amount?: number;
	corporate_customer?: PriceCorporateCustomer;
	/** Credit limit (only for 'credit' accounts) */
	credit_limit?: number;
	/** Credit type */
	credit_type?: string;
	/** Customer account id */
	id?: string;
	limits?: IncustClientApiClientModelsAccountLimitsAccountLimits;
	/** Odometer value needed flag (optional, for "retail-special-account" only) */
	odometer?: string;
	retail_customer?: PriceRetailCustomer;
	/** Additional PIN-code for access to the account (optional, for "retail-special-account" only) */
	security_code?: string;
}
