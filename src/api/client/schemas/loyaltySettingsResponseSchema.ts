/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { LoyaltySettingsTypeClientAuth } from "./loyaltySettingsTypeClientAuth";
import type { LoyaltySettingsApplicableTypes } from "./loyaltySettingsApplicableTypes";

/**
 * Схема для налаштувань лояльності
 */
export interface LoyaltySettingsResponseSchema {
	id: number;
	type_client_auth?: LoyaltySettingsTypeClientAuth;
	prohibit_redeeming_bonuses?: boolean;
	prohibit_redeeming_coupons?: boolean;
	loyalty_applicable_type?: LoyaltySettingsApplicableTypes;
	is_enabled?: boolean;
}
