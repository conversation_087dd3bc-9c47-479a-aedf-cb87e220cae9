/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * CustomerPaymentSystemType
 */
export type IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType =
	(typeof IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType)[keyof typeof IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType = {
	ipay: "ipay",
	stripe: "stripe",
	amp: "amp",
	"4bill": "4bill",
	eway: "eway",
	flutterwave: "flutterwave",
	"global-payments": "global-payments",
	wave: "wave",
	pax: "pax",
} as const;
