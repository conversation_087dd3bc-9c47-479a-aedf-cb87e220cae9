/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsPriceExplainValueSourcePriceExplainValueSource } from "./incustTerminalApiClientModelsPriceExplainValueSourcePriceExplainValueSource";
import type { IncustTerminalApiClientModelsPriceExplainValueTypePriceExplainValueType } from "./incustTerminalApiClientModelsPriceExplainValueTypePriceExplainValueType";

/**
 * CheckMarginExplainDetailingItem
 */
export interface IncustTerminalApiClientModelsCheckMarginExplainDetailingItemCheckMarginExplainDetailingItem {
	/** Number of decimal digits in value */
	decimal_digits?: number;
	/** Include in receipt (optional) */
	include_in_receipt?: boolean;
	source?: IncustTerminalApiClientModelsPriceExplainValueSourcePriceExplainValueSource;
	type?: IncustTerminalApiClientModelsPriceExplainValueTypePriceExplainValueType;
	/** Result value of current price level */
	value?: number;
	/** Value Code if exists (optional) */
	value_code?: string;
	/** Value Id if exists (optional) */
	value_id?: string;
	/** Value title if exists (optional) */
	value_title?: string;
}
