/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * UserStatus
 */
export type UserStatus = (typeof UserStatus)[keyof typeof UserStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UserStatus = {
	deleted: "deleted",
	blocked: "blocked",
	banned: "banned",
	suspended: "suspended",
	registered: "registered",
	activated: "activated",
} as const;
