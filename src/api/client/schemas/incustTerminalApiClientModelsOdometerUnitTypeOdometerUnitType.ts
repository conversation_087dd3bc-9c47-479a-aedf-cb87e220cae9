/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * OdometerUnitType
 */
export type IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType =
	(typeof IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType)[keyof typeof IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustTerminalApiClientModelsOdometerUnitTypeOdometerUnitType = {
	K: "K",
	M: "M",
	H: "H",
} as const;
