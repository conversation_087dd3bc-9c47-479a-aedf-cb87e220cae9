/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";

/**
 * FeedbackSource
 */
export interface IncustClientApiClientModelsFeedbackSourceFeedbackSource {
	/** Allow add picture to the feddback flag */
	allow_pictures?: number;
	/** Allow add picture to the feddback flag */
	allow_video?: number;
	/** Allow add picture to the feddback flag */
	allow_voice?: number;
	/** Allowed/required customers action */
	customer_actions?: string;
	/** Feedback source ID */
	id?: string;
	/** Text displayed for customers as invitation */
	invitation_text?: string;
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	/** Title */
	title?: string;
}
