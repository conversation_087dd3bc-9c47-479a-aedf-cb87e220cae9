/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes } from "./incustTerminalApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes";
import type { PaymentStatusEnum } from "./paymentStatusEnum";
import type { IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType } from "./incustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType";

/**
 * BankCardExternalInfo
 */
export interface IncustTerminalApiClientModelsBankCardExternalInfoBankCardExternalInfo {
	edc_card_type?: IncustTerminalApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes;
	/** Payment Id */
	id?: string;
	status?: PaymentStatusEnum;
	type?: IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType;
}
