/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * LoyaltyRuleDependsTimeZone
 */
export type LoyaltyRuleDependsTimeZone =
	(typeof LoyaltyRuleDependsTimeZone)[keyof typeof LoyaltyRuleDependsTimeZone];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LoyaltyRuleDependsTimeZone = {
	system: "system",
	pos: "pos",
} as const;
