/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsUserSignatureUserSignature } from "./incustClientApiClientModelsUserSignatureUserSignature";

/**
 * UserExternalForm
 */
export interface IncustClientApiClientModelsUserExternalFormUserExternalForm {
	/** Form filled date (optional, for filled forms only, readonly) */
	created?: string;
	/** External Form Id (readonly) */
	external_form_id?: string;
	/** Empty form file URL */
	file?: string;
	/** Form data object */
	form_data?: unknown;
	/** User Form Id (optional, readonly) */
	id?: string;
	signature?: IncustClientApiClientModelsUserSignatureUserSignature;
	/** Form signed by user (optional, for filled forms only, readonly) */
	signed?: boolean;
	/** Form title */
	title?: string;
	/** Form type */
	type?: string;
	/** User data object */
	user_data?: unknown;
	/** User Id (optional, readonly) */
	user_id?: string;
}
