/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * PriceExplainOriginValueOption
 */
export type IncustTerminalApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption =
	(typeof IncustTerminalApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption)[keyof typeof IncustTerminalApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustTerminalApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption =
	{
		discount: "discount",
		markup: "markup",
	} as const;
