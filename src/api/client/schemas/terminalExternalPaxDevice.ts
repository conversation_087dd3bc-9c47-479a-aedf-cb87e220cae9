/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { TerminalExternalPaxDeviceSettings } from "./terminalExternalPaxDeviceSettings";

/**
 * TerminalExternalPaxDevice
 */
export interface TerminalExternalPaxDevice {
	/** Device connection type */
	connection_type?: string;
	/** Device settings (depends on device type) */
	settings?: TerminalExternalPaxDeviceSettings;
}
