/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory } from "./incustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory";
import type { LoyaltyLinks } from "./loyaltyLinks";

/**
 * Loyalty
 */
export interface IncustTerminalApiClientModelsLoyaltyLoyalty {
	/** Categories */
	categories?: IncustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory[];
	/** Loyalty Id (readonly) */
	id?: string;
	/** Loyalty legal info */
	legal_info?: string;
	links?: LoyaltyLinks;
	photos?: string[];
	/** Loyalty privacy policy link */
	privacy_policy_url?: string;
	title?: string;
}
