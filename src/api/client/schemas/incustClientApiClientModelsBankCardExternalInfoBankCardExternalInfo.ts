/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes } from "./incustClientApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes";
import type { IncustClientApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType } from "./incustClientApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType";

/**
 * BankCardExternalInfo
 */
export interface IncustClientApiClientModelsBankCardExternalInfoBankCardExternalInfo {
	edc_card_type?: IncustClientApiClientModelsExternalBankCardsEdcTypesExternalBankCardsEDCTypes;
	/** Payment Id */
	id?: string;
	/** Payment status */
	status?: string;
	type?: IncustClientApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType;
}
