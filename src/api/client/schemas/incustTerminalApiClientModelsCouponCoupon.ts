/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsCouponBatchCouponBatch } from "./incustTerminalApiClientModelsCouponBatchCouponBatch";

/**
 * Coupon
 */
export interface IncustTerminalApiClientModelsCouponCoupon {
	/** Coupon check applicable for check (optional, check only) */
	applicable?: boolean;
	batch?: IncustTerminalApiClientModelsCouponBatchCouponBatch;
	/** Business code */
	business_code?: string;
	/** Coupon certificate type refill amount (optional) */
	charge_amount?: number;
	/** currency (optional) */
	charge_bonuses_currency?: string;
	/** Value of promotional bonuses expire date (optional) */
	charge_promotional_bonuses_expire_date?: string;
	/** Promotional bonuses expiration value type (optional) */
	charge_promotional_bonuses_expire_type?: string;
	/** Value of promotional bonuses expire (optional) */
	charge_promotional_bonuses_expire_value?: number;
	/** Special account currency (optional) */
	charge_special_account_currency?: string;
	/** Special account ID (optional) */
	charge_special_account_id?: string;
	/** Special account title (optional) */
	charge_special_account_title?: string;
	/** Coupon certificate type refill object (optional) */
	charge_type?: string;
	/** Coupon code */
	code?: string;
	/** Coupon Description */
	description?: string;
	/** Do not add emitted coupons to the customer's wallet (optional, check only) */
	do_not_add_to_wallet?: boolean;
	/** Coupon check implementation error (optional, check only) */
	error?: string;
	/** Coupon external_code (optional) */
	external_code?: string;
	/** External code visibility type (optional) */
	external_code_visibility_type?: string;
	/** Gift Card ID (optional) */
	gift_card_id?: string;
	/** Unique id of coupon */
	id?: string;
	image?: string;
	/** Coupon locked by transaction process (optional) */
	locked?: boolean;
	/** Loyalty emission id */
	loyalty_id?: string;
	/** Coupon processing status (optional, check by transaction only) */
	processing_status?: string;
	/** Redeem only at terminal restriction */
	redeem_at_terminal?: number;
	/** Coupon redeemed transaction Id (optional) */
	redeemed_transaction_id?: string;
	/** Coupons share allowed for customer */
	share_allowed?: number;
	/** Status of coupon */
	status?: string;
	/** Coupon Title */
	title?: string;
	/** Type of coupon */
	type?: string;
	/** Coupon valid flag (optional) */
	valid?: boolean;
}
