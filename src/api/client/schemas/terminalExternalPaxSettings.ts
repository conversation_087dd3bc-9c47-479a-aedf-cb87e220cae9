/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { TerminalExternalPaxDevice } from "./terminalExternalPaxDevice";

/**
 * TerminalExternalPaxSettings
 */
export interface TerminalExternalPaxSettings {
	devices?: TerminalExternalPaxDevice[];
	/** Allow PAX terminal not check for duplicate, also it will override the local duplicate check (default true) */
	ignore_duplicate_transactions?: boolean;
	/** Send entry mode in AUTH requests (default true) */
	send_entry_mode?: boolean;
	/** Send the fuel amount (fleet goods) regardless of the payment amount (default false) */
	send_fuel_amount?: boolean;
	/** Use AUTH with zero amount (default false) */
	zero_amount_auth?: boolean;
}
