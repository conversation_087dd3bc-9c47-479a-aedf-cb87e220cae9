/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCorporateCustomerInfoCorporateCustomerInfo } from "./incustClientApiClientModelsCorporateCustomerInfoCorporateCustomerInfo";
import type { AccountsRec } from "./accountsRec";

/**
 * Corporate customer  # noqa: E501
 */
export interface CorporateCustomerAccountInfo {
	/** Account active flag */
	active?: number;
	corporate_customer?: IncustClientApiClientModelsCorporateCustomerInfoCorporateCustomerInfo;
	/** Corporate customer special account ID */
	id?: string;
	special_account?: AccountsRec;
}
