/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * RuleCalculationBaseItemData
 */
export interface RuleCalculationBaseItemData {
	/** Item amount */
	amount?: number;
	/** Item total bonus points redeemed calculated by check */
	bonuses_redeemed_amount?: number;
	/** Check item index */
	index?: number;
	/** Item price */
	price?: number;
	/** Item quantity */
	quantity?: number;
	/** Item unit bonus points redeemed calculated by check */
	unit_bonuses_redeemed_amount?: number;
}
