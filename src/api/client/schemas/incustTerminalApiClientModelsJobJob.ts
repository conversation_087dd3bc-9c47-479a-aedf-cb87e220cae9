/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsJobDetailsJobDetails } from "./incustTerminalApiClientModelsJobDetailsJobDetails";
import type { IncustTerminalApiClientModelsTransactionErrorsTransactionErrors } from "./incustTerminalApiClientModelsTransactionErrorsTransactionErrors";

/**
 * Job
 */
export interface IncustTerminalApiClientModelsJobJob {
	/** Automaticaly capture funds of authorized payment transactions when job finished */
	auto_capture_parent_transaction?: boolean;
	/** Automaticaly start the job after creation */
	auto_start?: boolean;
	/** Corporate User ID (optional) */
	corporate_user_id?: string;
	/** Created time */
	created?: string;
	details?: IncustTerminalApiClientModelsJobDetailsJobDetails;
	/** Capturing transaction errors list */
	finalize_transaction_errors?: IncustTerminalApiClientModelsTransactionErrorsTransactionErrors[];
	/** Flag of finalize transaction failed. Exists and set to TRUE if capture funds of authorized payment failed */
	finalize_transaction_failed?: boolean;
	/** Сapture funds of authorized payment or cancellation transaction ID (optional) */
	finalize_transaction_id?: string;
	/** Finished time */
	finished?: string;
	/** Job ID */
	id?: string;
	/** Started time */
	started?: string;
	/** Job status */
	status?: string;
	/** Linked transaction ID (optional) */
	transaction_id?: string;
	/** Job type (required for Job creation) */
	type?: string;
	/** User ID (optional) */
	user_id?: string;
}
