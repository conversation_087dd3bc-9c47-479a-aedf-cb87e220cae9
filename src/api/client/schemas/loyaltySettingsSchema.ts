/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { LoyaltySettingsTypeClientAuth } from "./loyaltySettingsTypeClientAuth";
import type { LoyaltySettingsApplicableTypes } from "./loyaltySettingsApplicableTypes";
import type { LoyaltySettingsSchemaJsonData } from "./loyaltySettingsSchemaJsonData";

/**
 * Схема для налаштувань лояльності, яка працює поза контекстом БД
 */
export interface LoyaltySettingsSchema {
	terminal_api_key: string;
	server_url: string;
	white_label_id?: string;
	terminal_id?: string;
	loyalty_id?: string;
	type_client_auth?: LoyaltySettingsTypeClientAuth;
	prohibit_redeeming_bonuses?: boolean;
	prohibit_redeeming_coupons?: boolean;
	loyalty_applicable_type?: LoyaltySettingsApplicableTypes;
	priority?: number;
	is_enabled?: boolean;
	name?: string;
	description?: string;
	brand_id?: number;
	ewallet_id?: number;
	ewallet_merchant_id?: number;
	store_id?: number;
	product_id?: number;
	profile_id?: number;
	invoice_template_id?: number;
	json_data?: LoyaltySettingsSchemaJsonData;
}
