/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * CheckService
 */
export interface IncustTerminalApiClientModelsCheckServiceCheckService {
	/** Unique Id of additional service by code (optional) */
	additional_service_id?: string;
	/** Service amount */
	amount?: number;
	/** Service code */
	code: string;
	/** Service cost (for unit of value) */
	cost?: number;
	/** Number of decimal digits in cost and amount values */
	decimal_digits?: number;
	/** Service title */
	title?: string;
	/** Service type */
	type?: string;
	/** Base value for calculate service cost and amount (distance, etc.) */
	value?: number;
}
