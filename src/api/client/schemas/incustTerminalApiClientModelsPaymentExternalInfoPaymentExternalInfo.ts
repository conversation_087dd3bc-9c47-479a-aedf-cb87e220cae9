/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType } from "./incustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType";

/**
 * PaymentExternalInfo
 */
export interface IncustTerminalApiClientModelsPaymentExternalInfoPaymentExternalInfo {
	/** Software version */
	app_version?: string;
	/** Authorization code */
	auth_code?: string;
	/** Bank response code */
	bank_response_code?: string;
	/** Batch number */
	batch_number?: string;
	/** Card holder name */
	card_holder_name?: string;
	/** Card issuer name */
	card_issuer_name?: string;
	/** Card label */
	card_label?: string;
	/** Card wallet like 'Apple Pay', 'Google Pay', 'Masterpass', 'Visa Checkout', etc (optional) */
	card_wallet?: string;
	/** Card verification method */
	cvm?: string;
	/** EMV Application Cryptogram */
	emv_ac?: string;
	/** EMV Application Identifier */
	emv_aid?: string;
	/** EMV Application Name */
	emv_aid_name?: string;
	/** EMV Application Transaction Counter */
	emv_atc?: string;
	/** EMV Issuer Application Data */
	emv_iad?: string;
	/** EMV Transaction Status Indicator */
	emv_tsi?: string;
	/** EMV Transaction Verification Results */
	emv_tvr?: string;
	/** Card entry mode */
	entry_mode?: string;
	/** Receipt gateway transaction Id */
	gateway_transaction_id?: string;
	/** Host timestamp */
	host_timestamp?: string;
	/** Invoice/receipt number */
	invoice_number?: string;
	/** Merchant Id */
	merchant_id?: string;
	/** Masked primary account number */
	pan?: string;
	/** Response code from host */
	response_code?: string;
	/** ISO response code */
	response_code_iso?: string;
	/** Response text from host */
	response_text?: string;
	/** Sequence number */
	sequence_number?: string;
	system_type?: IncustTerminalApiClientModelsCustomerPaymentSystemTypeCustomerPaymentSystemType;
	/** Termsnal Id */
	terminal_id?: string;
	/** Receipt transaction name */
	transaction_name?: string;
	/** Receipt transaction reference number */
	transaction_reference_number?: string;
}
