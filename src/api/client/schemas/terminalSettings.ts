/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { TerminalCustomerFeedbackSettings } from "./terminalCustomerFeedbackSettings";
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";
import type { LoyaltyBonusesSettings } from "./loyaltyBonusesSettings";
import type { IncustClientApiClientModelsPosPos } from "./incustClientApiClientModelsPosPos";
import type { IncustClientApiClientModelsTerminalVehicleFuelSettingsTerminalVehicleFuelSettings } from "./incustClientApiClientModelsTerminalVehicleFuelSettingsTerminalVehicleFuelSettings";

/**
 * Terminal settings  # noqa: E501
 */
export interface TerminalSettings {
	/** Currency ISO code */
	currency?: string;
	customer_feedback_settings?: TerminalCustomerFeedbackSettings;
	/** Only preset recommended values allowed */
	goods_preset_recommnded_values_only?: number;
	/** Terminal Id */
	id?: string;
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	loyalty_bonuses_settings?: LoyaltyBonusesSettings;
	/** Mobile app payment of an arbitrary amount allowed */
	mobile_app_payment_amount_free?: number;
	pos?: IncustClientApiClientModelsPosPos;
	vehicle_fuel_settings?: IncustClientApiClientModelsTerminalVehicleFuelSettingsTerminalVehicleFuelSettings;
}
