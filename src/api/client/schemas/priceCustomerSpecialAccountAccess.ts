/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits } from "./incustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits";

/**
 * PriceCustomerSpecialAccountAccess
 */
export interface PriceCustomerSpecialAccountAccess {
	/** Amount of funds in the account available for using (null if unlimited)(optional, for extended accounts info only) */
	available_amount?: number;
	/** Customer access id */
	id?: string;
	limits?: IncustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits;
	/** Odometer value needed flag */
	odometer?: string;
	/** Additional PIN-code for access to the account */
	security_code?: string;
	/** Vehicle QR Code / Tag needed flag */
	vehicle_id?: string;
	/** Vehicle QR Code / Tag needed to be verify by Vehicles/Inventory list */
	vehicle_id_verify?: number;
}
