/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsCouponCoupon } from "./incustTerminalApiClientModelsCouponCoupon";
import type { CustomerBonusesAccount } from "./customerBonusesAccount";
import type { AccountsRecExt } from "./accountsRecExt";
import type { ImageClient } from "./imageClient";

/**
 * Кастомна схема гаманця з Terminal API типами
 */
export interface IncustWalletsExt {
	coupons?: IncustTerminalApiClientModelsCouponCoupon[];
	bonuses?: CustomerBonusesAccount[];
	accounts?: AccountsRecExt[];
	description?: string;
	favorite?: boolean;
	id?: string;
	title?: string;
	type?: string;
	photos?: ImageClient[];
}
