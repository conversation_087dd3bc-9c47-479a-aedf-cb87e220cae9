/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCheckTaxExplainDetailingItemCheckTaxExplainDetailingItem } from "./incustClientApiClientModelsCheckTaxExplainDetailingItemCheckTaxExplainDetailingItem";

/**
 * CheckTaxes
 */
export interface IncustClientApiClientModelsCheckTaxesCheckTaxes {
	/** Base taxes detailing */
	base?: IncustClientApiClientModelsCheckTaxExplainDetailingItemCheckTaxExplainDetailingItem[];
	/** Sales taxes detailing */
	sales?: IncustClientApiClientModelsCheckTaxExplainDetailingItemCheckTaxExplainDetailingItem[];
}
