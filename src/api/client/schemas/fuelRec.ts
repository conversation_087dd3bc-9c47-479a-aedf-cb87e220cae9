/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsFuelControllerConfigurationFuelControllerConfiguration } from "./incustClientApiClientModelsFuelControllerConfigurationFuelControllerConfiguration";
import type { FuelPumpRec } from "./fuelPumpRec";

/**
 * FuelRec
 */
export interface FuelRec {
	configuration?: IncustClientApiClientModelsFuelControllerConfigurationFuelControllerConfiguration;
	/** Configuration reading status (read only) */
	configuration_reading_status?: string;
	/** Controller configuration last updated time */
	configuration_updated?: string;
	/** Controller ID */
	id?: string;
	/** Pump authorize confirmation type */
	pump_authorize_confirmation?: string;
	/** Pump authorize type */
	pump_authorize_type?: string;
	/** Pumps info */
	pumps?: FuelPumpRec[];
	/** Skip pumps when selecting fuel for filling */
	skip_pumps_in_selection?: number;
}
