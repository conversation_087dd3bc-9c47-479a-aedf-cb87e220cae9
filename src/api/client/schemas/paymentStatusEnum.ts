/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * An enumeration of possible statuses for PaymentStatus.
 */
export type PaymentStatusEnum = (typeof PaymentStatusEnum)[keyof typeof PaymentStatusEnum];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PaymentStatusEnum = {
	inprocess: "inprocess",
	success: "success",
	fail: "fail",
	canceled: "canceled",
	error: "error",
	authorized: "authorized",
	verification: "verification",
	registered: "registered",
	unknown: "unknown",
} as const;
