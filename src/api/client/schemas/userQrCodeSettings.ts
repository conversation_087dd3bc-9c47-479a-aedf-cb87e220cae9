/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { UserQrCodeLegacy } from "./userQrCodeLegacy";

/**
 * UserQrCodeSettings
 */
export interface UserQrCodeSettings {
	/** User QR code regex (readonly) */
	code_regex?: string;
	legacy?: UserQrCodeLegacy[];
	/** Current version of user QR code (readonly) */
	version?: number;
	/** Version of user QR code regex (readonly) */
	version_regex?: string;
}
