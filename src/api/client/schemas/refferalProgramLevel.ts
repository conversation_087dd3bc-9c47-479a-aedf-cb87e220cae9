/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsThresholdsValuesThresholdsValues } from "./incustClientApiClientModelsThresholdsValuesThresholdsValues";

/**
 * RefferalProgramLevel
 */
export interface RefferalProgramLevel {
	/** Level number` */
	level?: number;
	/** Reward value (optional, if value_depends_type is 'none') */
	value?: number;
	/** Reward value depends on value matrix (optional, if value_depends_type is not 'none') */
	value_depends_matrix?: IncustClientApiClientModelsThresholdsValuesThresholdsValues[];
	/** Reward value depends on */
	value_depends_type?: string;
}
