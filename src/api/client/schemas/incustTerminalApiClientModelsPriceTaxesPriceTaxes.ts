/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem } from "./incustTerminalApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem";

/**
 * PriceTaxes
 */
export interface IncustTerminalApiClientModelsPriceTaxesPriceTaxes {
	/** Base taxes detailing */
	base?: IncustTerminalApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem[];
	/** Sales taxes detailing */
	sales?: IncustTerminalApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem[];
}
