/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsAdditionalServiceAdditionalService } from "./incustTerminalApiClientModelsAdditionalServiceAdditionalService";
import type { IncustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory } from "./incustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory";
import type { LoyaltyLinks } from "./loyaltyLinks";
import type { IncustTerminalApiClientModelsPhoneNumberPhoneNumber } from "./incustTerminalApiClientModelsPhoneNumberPhoneNumber";
import type { IncustTerminalApiClientModelsImageImage } from "./incustTerminalApiClientModelsImageImage";

/**
 * Point of sale  # noqa: E501
 */
export interface IncustTerminalApiClientModelsPosPos {
	/** Additional services (optional) */
	additional_services?: IncustTerminalApiClientModelsAdditionalServiceAdditionalService[];
	/** Is additional services required */
	additional_services_required?: number;
	/** Point of sales address */
	addresses?: string;
	/** Categories */
	categories?: IncustTerminalApiClientModelsLoyaltyCategoryLoyaltyCategory[];
	/** Pos check items entering mode possibilities */
	check_item_enter_mode?: string;
	/** Point of sales country code */
	country?: string;
	/** Point of sales ID */
	id?: string;
	links?: LoyaltyLinks;
	/** Maximum service distance in km (0 - unlimited) */
	maximum_service_distance?: number;
	/** Phone prefix in international format. Sample '+380' */
	phone_prefix?: string;
	/** Local interpretation of phone prefix in international format. Sample '+380' */
	phone_prefix_local?: string;
	/** Phones of sale point */
	phones?: IncustTerminalApiClientModelsPhoneNumberPhoneNumber[];
	photos?: IncustTerminalApiClientModelsImageImage[];
	/** Point of sales title */
	title?: string;
	/** Pos type */
	type?: string;
}
