/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsJobEquipmentJobEquipment } from "./incustClientApiClientModelsJobEquipmentJobEquipment";
import type { IncustClientApiClientModelsJobFuelJobFuel } from "./incustClientApiClientModelsJobFuelJobFuel";

/**
 * JobDetails
 */
export interface IncustClientApiClientModelsJobDetailsJobDetails {
	equipment?: IncustClientApiClientModelsJobEquipmentJobEquipment;
	fuel?: IncustClientApiClientModelsJobFuelJobFuel;
}
