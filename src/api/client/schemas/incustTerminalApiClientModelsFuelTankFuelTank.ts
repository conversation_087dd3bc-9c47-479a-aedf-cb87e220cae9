/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsProbeMeasurementsProbeMeasurements } from "./incustTerminalApiClientModelsProbeMeasurementsProbeMeasurements";
import type { IncustTerminalApiClientModelsTankConfigurationTankConfiguration } from "./incustTerminalApiClientModelsTankConfigurationTankConfiguration";

/**
 * FuelTank
 */
export interface IncustTerminalApiClientModelsFuelTankFuelTank {
	actual_measurements?: IncustTerminalApiClientModelsProbeMeasurementsProbeMeasurements;
	configuration?: IncustTerminalApiClientModelsTankConfigurationTankConfiguration;
	finish_measurements?: IncustTerminalApiClientModelsProbeMeasurementsProbeMeasurements;
	/** Grade code */
	grade_code?: string;
	/** Grade ID */
	grade_id?: string;
	/** Tank ID */
	id?: string;
	/** Internal tank ID */
	internal_id?: string;
	start_measurements?: IncustTerminalApiClientModelsProbeMeasurementsProbeMeasurements;
}
