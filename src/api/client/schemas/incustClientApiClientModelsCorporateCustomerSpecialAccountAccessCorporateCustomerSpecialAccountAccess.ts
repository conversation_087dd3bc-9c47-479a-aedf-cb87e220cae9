/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { CorporateCustomerAccountInfo } from "./corporateCustomerAccountInfo";
import type { IncustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits } from "./incustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits";

/**
 * CorporateCustomerSpecialAccountAccess
 */
export interface IncustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess {
	/** Amount of funds on the account available for using (null if unlimited) */
	available_amount?: number;
	corporate_customer_special_account?: CorporateCustomerAccountInfo;
	/** Account applicable goods filter */
	goods_items?: string[];
	/** Access ID */
	id?: string;
	limits?: IncustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits;
	/** Odometer value needed flag */
	odometer?: string;
	/** Additional PIN-code for access to the account exists */
	security_code?: number;
	/** Vehicle ID needed flag */
	vehicle_id?: string;
}
