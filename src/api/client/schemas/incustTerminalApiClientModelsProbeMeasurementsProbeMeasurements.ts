/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsProbeAlarmProbeAlarm } from "./incustTerminalApiClientModelsProbeAlarmProbeAlarm";

/**
 * ProbeMeasurements
 */
export interface IncustTerminalApiClientModelsProbeMeasurementsProbeMeasurements {
	/** Alarms (if present) */
	alarms?: IncustTerminalApiClientModelsProbeAlarmProbeAlarm[];
	/** Controller ID */
	controller_id?: string;
	/** Measurements time */
	processed?: string;
	/** Value of product density in in 0.1 kg/m3 units */
	product_density?: number;
	/** Value of product height in 0.1 mm */
	product_height?: number;
	/** Value of product mass in 0.1 kg */
	product_mass?: number;
	/** Value of product temperature compensated volume in liters */
	product_tc_volume?: number;
	/** Value of product ullage in liters */
	product_ullage?: number;
	/** Value of product volume in liters */
	product_volume?: number;
	/** Status of probe and measurements */
	status?: string;
	/** Value of tank filling in percentage */
	tank_filling_percentage?: number;
	/** Tank ID */
	tank_id?: string;
	/** Value product temperature in 0.1 degrees Celcium */
	temperature?: number;
	/** Value of water height in 0.1 mm */
	water_height?: number;
	/** Value of water volume in liters */
	water_volume?: number;
}
