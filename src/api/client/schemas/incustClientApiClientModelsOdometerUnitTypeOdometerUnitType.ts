/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * OdometerUnitType
 */
export type IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType =
	(typeof IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType)[keyof typeof IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustClientApiClientModelsOdometerUnitTypeOdometerUnitType = {
	K: "K",
	M: "M",
	H: "H",
} as const;
