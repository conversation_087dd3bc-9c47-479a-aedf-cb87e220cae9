/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsBonusAddedBonusAdded } from "./incustTerminalApiClientModelsBonusAddedBonusAdded";
import type { RuleCalculationBaseData } from "./ruleCalculationBaseData";
import type { IncustTerminalApiClientModelsCouponCoupon } from "./incustTerminalApiClientModelsCouponCoupon";
import type { RuleImplementationResultItemData } from "./ruleImplementationResultItemData";
import type { IncustTerminalApiClientModelsSpecialAccountChargeSpecialAccountCharge } from "./incustTerminalApiClientModelsSpecialAccountChargeSpecialAccountCharge";

/**
 * RuleImplementationResultData
 */
export interface RuleImplementationResultData {
	/** Bonuses added (optional) */
	bonuses_added?: IncustTerminalApiClientModelsBonusAddedBonusAdded[];
	calculation_base?: RuleCalculationBaseData;
	/** Discount amount (optional) */
	discount?: number;
	/** Emitted coupons (optional) */
	emitted_coupons?: IncustTerminalApiClientModelsCouponCoupon[];
	/** Implementation error (optional) */
	error?: string;
	/** Implementation error code (optional) */
	error_code?: number;
	/** Result by items (optional) */
	items?: RuleImplementationResultItemData[];
	/** Special account charges (optional) */
	special_accounts_charges?: IncustTerminalApiClientModelsSpecialAccountChargeSpecialAccountCharge[];
}
