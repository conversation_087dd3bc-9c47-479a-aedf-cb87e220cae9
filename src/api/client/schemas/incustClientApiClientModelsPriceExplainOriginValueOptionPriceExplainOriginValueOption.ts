/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * PriceExplainOriginValueOption
 */
export type IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption =
	(typeof IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption)[keyof typeof IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption =
	{
		discount: "discount",
		markup: "markup",
	} as const;
