/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsProbeMeasurementsProbeMeasurements } from "./incustClientApiClientModelsProbeMeasurementsProbeMeasurements";
import type { IncustClientApiClientModelsTankConfigurationTankConfiguration } from "./incustClientApiClientModelsTankConfigurationTankConfiguration";

/**
 * FuelTank
 */
export interface IncustClientApiClientModelsFuelTankFuelTank {
	actual_measurements?: IncustClientApiClientModelsProbeMeasurementsProbeMeasurements;
	configuration?: IncustClientApiClientModelsTankConfigurationTankConfiguration;
	finish_measurements?: IncustClientApiClientModelsProbeMeasurementsProbeMeasurements;
	/** Grade code */
	grade_code?: string;
	/** Grade ID */
	grade_id?: string;
	/** Tank ID */
	id?: string;
	/** Internal tank ID */
	internal_id?: string;
	start_measurements?: IncustClientApiClientModelsProbeMeasurementsProbeMeasurements;
}
