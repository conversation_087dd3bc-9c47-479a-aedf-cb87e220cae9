/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits } from "./incustClientApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits";
import type { IncustClientApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits } from "./incustClientApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits";

/**
 * FuelControllerConfiguration
 */
export interface IncustClientApiClientModelsFuelControllerConfigurationFuelControllerConfiguration {
	decimal_digits?: IncustClientApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits;
	measurement_units?: IncustClientApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits;
}
