/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsBankCardExternalInfoBankCardExternalInfo } from "./incustClientApiClientModelsBankCardExternalInfoBankCardExternalInfo";
import type { IncustClientApiClientModelsPaymentExternalInfoPaymentExternalInfo } from "./incustClientApiClientModelsPaymentExternalInfoPaymentExternalInfo";
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";
import type { IncustClientApiClientModelsMobileAppInfoMobileAppInfo } from "./incustClientApiClientModelsMobileAppInfoMobileAppInfo";
import type { IncustClientApiClientModelsPosPos } from "./incustClientApiClientModelsPosPos";
import type { Terminal } from "./terminal";
import type { IncustClientApiClientModelsTransactionTransaction } from "./incustClientApiClientModelsTransactionTransaction";

/**
 * Payment status details  # noqa: E501
 */
export interface PaymentInfo {
	/** To pay amount */
	amount?: number;
	bank_card_external?: IncustClientApiClientModelsBankCardExternalInfoBankCardExternalInfo;
	/** Payment change (optional, only for 'cash' type) */
	change?: number;
	/** currency */
	currency?: string;
	/** Customer fee amount (optional, only for 'mobile-app' type) */
	customer_fee_amount?: number;
	external_info?: IncustClientApiClientModelsPaymentExternalInfoPaymentExternalInfo;
	/** Payment Id */
	id?: string;
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	mobileapp?: IncustClientApiClientModelsMobileAppInfoMobileAppInfo;
	pos?: IncustClientApiClientModelsPosPos;
	/** Payment receipt (optional, only for 'cash' type) */
	receipt?: number;
	/** Operation status */
	status?: string;
	terminal?: Terminal;
	transaction?: IncustClientApiClientModelsTransactionTransaction;
	/** Wallet type */
	type?: string;
}
