/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { CorporateUserInfo } from "./corporateUserInfo";

/**
 * Corporate customer  # noqa: E501
 */
export interface IncustClientApiClientModelsCorporateCustomerInfoCorporateCustomerInfo {
	corporate_user?: CorporateUserInfo;
	/** Corporate customer ID */
	id?: string;
	/** Customer suspended flag */
	suspended?: number;
}
