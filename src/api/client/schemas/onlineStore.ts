/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { PosOnlineStore } from "./posOnlineStore";

/**
 * OnlineStore
 */
export interface OnlineStore {
	/** Object active flag */
	active?: number;
	/** Additional customer info (optional) */
	additional_customer_info?: string;
	/** Users cart expire period in days (optional) */
	cart_expire_days?: number;
	/** Order type used in schema */
	order_type?: string;
	/** Payment in store (optional) */
	payment_type?: string;
	pos_level_store?: PosOnlineStore;
	/** Loyalty has active Pos with online-store type */
	pos_level_stores_exists?: number;
	/** Terminal ID (optional) */
	terminal_id?: string;
	/** Title (optional) */
	title?: string;
}
