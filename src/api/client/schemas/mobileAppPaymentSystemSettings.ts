/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { MobileAppPaymentSettingsFee } from "./mobileAppPaymentSettingsFee";

/**
 * MobileAppPaymentSystemSettings
 */
export interface MobileAppPaymentSystemSettings {
	/** Submerchant API key (optional, for eway only) */
	api_key?: string;
	application_fee?: MobileAppPaymentSettingsFee;
	customer_application_fee?: MobileAppPaymentSettingsFee;
	/** Submerchant API key (optional, for eway only) */
	password?: string;
	/** Settings scope */
	scope?: string;
	/** Submerchant payment by card token service Id (optional, for 4bill only) */
	service_id_payment_by_token?: number;
	/** Submerchant card tokenization (by success payment) service Id (optional, for 4bill only) */
	service_id_tokenize_by_payment?: number;
	/** Submerchant Id */
	smch_id?: string;
	/** Amount from which 3DS verification needed (optional, for ipay only) */
	verification_limit?: number;
	/** Submerchant wallet Id (optional, for 4bill only) */
	wallet_id?: number;
}
