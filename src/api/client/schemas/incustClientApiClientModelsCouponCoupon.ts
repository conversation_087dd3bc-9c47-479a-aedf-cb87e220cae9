/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCouponBatchCouponBatch } from "./incustClientApiClientModelsCouponBatchCouponBatch";
import type { IncustClientApiClientModelsLoyaltyLoyalty } from "./incustClientApiClientModelsLoyaltyLoyalty";

/**
 * Coupon
 */
export interface IncustClientApiClientModelsCouponCoupon {
	/** Coupon check applicable for check (optional, check only) */
	applicable?: boolean;
	batch?: IncustClientApiClientModelsCouponBatchCouponBatch;
	/** Business code */
	business_code?: string;
	/** Coupon certificate type refill amount (optional) */
	charge_amount?: number;
	/** currency (optional) */
	charge_bonuses_currency?: string;
	/** Value of promotional bonuses expire date (optional) */
	charge_promotional_bonuses_expire_date?: string;
	/** Promotional bonuses expiration value type (optional) */
	charge_promotional_bonuses_expire_type?: string;
	/** Value of promotional bonuses expire (optional) */
	charge_promotional_bonuses_expire_value?: number;
	/** Special account currency (optional) */
	charge_special_account_currency?: string;
	/** Special account ID (optional) */
	charge_special_account_id?: string;
	/** Special account title (optional) */
	charge_special_account_title?: string;
	/** Coupon certificate type refill object (optional) */
	charge_type?: string;
	/** Coupon code */
	code?: string;
	/** Coupon emission price (optional) */
	customers_price_amount?: number;
	/** Coupon emission price currency (optional) */
	customers_price_currency?: string;
	/** Coupon emission price special account Id (optional) */
	customers_price_special_account_id?: string;
	/** Coupon emission price special account title (optional) */
	customers_price_special_account_title?: string;
	/** Coupon emission price type (optional) */
	customers_price_type?: string;
	/** Coupon Description */
	description?: string;
	/** Do not add emitted coupons to the customer's wallet (optional, check only) */
	do_not_add_to_wallet?: boolean;
	/** Date emission of coupon */
	emission_dt?: string;
	/** Coupon check implementation error (optional, check only) */
	error?: string;
	/** Date expiry of coupon */
	expire_date?: string;
	/** Coupon external_code */
	external_code?: string;
	/** External code visibility type */
	external_code_visibility_type?: string;
	/** Unique id of coupon */
	id?: string;
	image?: string;
	/** Coupon locked by transaction process (optional) */
	locked?: boolean;
	loyalty?: IncustClientApiClientModelsLoyaltyLoyalty;
	/** Loyalty emission id */
	loyalty_id?: string;
	/** Coupon processing status (optional, check by transaction only) */
	processing_status?: string;
	/** Recommendation fee amount */
	recommendation_fee_amount?: number;
	/** Recommendation fee bonuses currence ISO code (optional) */
	recommendation_fee_bonuses_currency?: string;
	/** Recommendation fee Batch CODE (optional) */
	recommendation_fee_coupon_batch_code?: string;
	/** Recommendation fee Batch Id (optional) */
	recommendation_fee_coupon_batch_id?: string;
	/** Recommendation fee Batch TITLE (optional) */
	recommendation_fee_coupon_batch_title?: string;
	/** Value of recommendation fee promotional bonuses expire date (optional) */
	recommendation_fee_promotional_bonuses_expire_date?: string;
	/** Recommendation fee expiration value type (optional) */
	recommendation_fee_promotional_bonuses_expire_type?: string;
	/** Value of recommendation fee promotional bonuses expire (optional) */
	recommendation_fee_promotional_bonuses_expire_value?: number;
	/** Special account ID (optional) */
	recommendation_fee_special_account_id?: string;
	/** Recommendation fee special account title (optional) */
	recommendation_fee_special_account_title?: string;
	/** Recommendation fee type */
	recommendation_fee_type?: string;
	/** Redeem only at terminal restriction */
	redeem_at_terminal?: number;
	/** Coupons share allowed for customer */
	share_allowed?: number;
	/** Status of coupon */
	status?: string;
	/** Coupon Title */
	title?: string;
	/** Type of coupon */
	type?: string;
}
