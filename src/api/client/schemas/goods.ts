/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsImageImage } from "./incustClientApiClientModelsImageImage";
import type { IncustClientApiClientModelsPriceMarginsPriceMargins } from "./incustClientApiClientModelsPriceMarginsPriceMargins";
import type { PreConfiguredValues } from "./preConfiguredValues";
import type { GoodsPrice } from "./goodsPrice";
import type { IncustClientApiClientModelsPriceTaxesPriceTaxes } from "./incustClientApiClientModelsPriceTaxesPriceTaxes";

/**
 * Goods
 */
export interface Goods {
	/** Goods additional images */
	additional_images?: IncustClientApiClientModelsImageImage[];
	/** Goods barcode */
	barcode?: string;
	/** Goods code */
	code?: string;
	/** Color */
	color?: number;
	/** Price currency */
	currency?: string;
	/** Description */
	description?: string;
	/** Goods ID */
	id?: string;
	image?: IncustClientApiClientModelsImageImage;
	margins?: IncustClientApiClientModelsPriceMarginsPriceMargins;
	/** Parent category code (optional) */
	parent_code?: string;
	/** Parent category ID */
	parent_id?: string;
	/** Parent category Title */
	parent_title?: string;
	/** Pre-configured values with amount or totals */
	preconfigured_values?: PreConfiguredValues[];
	/** Only preset recommended values allowed */
	preset_recommnded_values_only?: number;
	/** Googs price */
	price?: number;
	/** Number of decimal digits in price value (optional) */
	price_decimal_digits?: number;
	/** Price values */
	prices?: GoodsPrice[];
	/** Service types of goods */
	service_types?: string[];
	taxes?: IncustClientApiClientModelsPriceTaxesPriceTaxes;
	/** Title */
	title?: string;
	/** Unit */
	unit?: string;
	/** Price change allowed */
	variable_price?: number;
	/** Total change allowed */
	variable_total?: number;
	/** Weight goods flag */
	weighing?: number;
	/** Item quantity only by whole number flag */
	whole_number?: number;
}
