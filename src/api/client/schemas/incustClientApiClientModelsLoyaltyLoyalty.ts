/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { AccountsRec } from "./accountsRec";
import type { BonusesRec } from "./bonusesRec";
import type { CardCategory } from "./cardCategory";
import type { CardRecord } from "./cardRecord";
import type { IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory } from "./incustClientApiClientModelsLoyaltyCategoryLoyaltyCategory";
import type { IncustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess } from "./incustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess";
import type { IncustClientApiClientModelsCouponCoupon } from "./incustClientApiClientModelsCouponCoupon";
import type { CustomerAccess } from "./customerAccess";
import type { IncustClientApiClientModelsCustomerFeedbackSettingsCustomerFeedbackSettings } from "./incustClientApiClientModelsCustomerFeedbackSettingsCustomerFeedbackSettings";
import type { LoyaltyBonusesSettings } from "./loyaltyBonusesSettings";
import type { OnlineStore } from "./onlineStore";
import type { IncustClientApiClientModelsImageImage } from "./incustClientApiClientModelsImageImage";
import type { IncustClientApiClientModelsPosPos } from "./incustClientApiClientModelsPosPos";
import type { ReferralProgram } from "./referralProgram";

/**
 * Loyalty
 */
export interface IncustClientApiClientModelsLoyaltyLoyalty {
	accounts?: AccountsRec[];
	bonuses?: BonusesRec[];
	card_category?: CardCategory;
	cards?: CardRecord[];
	/** Categories */
	categories?: IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory[];
	/** List of access to the corporate customer special accounts */
	corporate_special_accounts_access?: IncustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess[];
	coupons?: IncustClientApiClientModelsCouponCoupon[];
	customer_access?: CustomerAccess;
	customer_feedback_settings?: IncustClientApiClientModelsCustomerFeedbackSettingsCustomerFeedbackSettings;
	description?: string;
	favorite?: boolean;
	/** Loyalty ID */
	id?: string;
	/** Loyalty legal info */
	legal_info?: string;
	links?: IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory;
	loyalty_bonuses_settings?: LoyaltyBonusesSettings;
	online_store?: OnlineStore;
	photos?: IncustClientApiClientModelsImageImage[];
	/** Points of sale */
	pos?: IncustClientApiClientModelsPosPos[];
	/** Loyalty privacy policy link */
	privacy_policy_url?: string;
	/** Loyalty public description (same as description) */
	public_description?: string;
	/** Loyalty public title (same as title) */
	public_title?: string;
	referral_program?: ReferralProgram;
	title?: string;
}
