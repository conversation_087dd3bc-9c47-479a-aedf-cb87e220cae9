/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource } from "./incustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource";
import type { IncustClientApiClientModelsPriceExplainValueTypePriceExplainValueType } from "./incustClientApiClientModelsPriceExplainValueTypePriceExplainValueType";
import type { IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption } from "./incustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption";
import type { IncustClientApiClientModelsPriceExplainOriginValueTypePriceExplainOriginValueType } from "./incustClientApiClientModelsPriceExplainOriginValueTypePriceExplainOriginValueType";

/**
 * PriceExplainDetailingItem
 */
export interface IncustClientApiClientModelsPriceExplainDetailingItemPriceExplainDetailingItem {
	/** Number of decimal digits in value */
	decimal_digits?: number;
	/** Tax/Margin exemption (optional) */
	exempt?: boolean;
	/** Include in receipt (optional) */
	include_in_receipt?: boolean;
	/** Initial value of current price level */
	initial_value?: number;
	/** Reverse calculation used (optional) */
	reverse_calculation?: boolean;
	source?: IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource;
	type?: IncustClientApiClientModelsPriceExplainValueTypePriceExplainValueType;
	/** Result value of current price level */
	value?: number;
	/** Option base value type */
	value_base_type?: string;
	/** Value Code if exists (optional) */
	value_code?: string;
	/** Value Id if exists (optional) */
	value_id?: string;
	value_option?: IncustClientApiClientModelsPriceExplainOriginValueOptionPriceExplainOriginValueOption;
	/** Original (base) value if exists (optional) */
	value_origin?: number;
	/** Value title if exists (optional) */
	value_title?: string;
	value_type?: IncustClientApiClientModelsPriceExplainOriginValueTypePriceExplainOriginValueType;
}
