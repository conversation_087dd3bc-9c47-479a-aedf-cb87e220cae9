/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * PosOnlineStore
 */
export interface PosOnlineStore {
	/** Online store active flag */
	active?: number;
	/** Users cart expire period in days */
	cart_expire_days?: number;
	/** Order type used in schema */
	order_type?: string;
	/** Payment in store */
	payment_type?: string;
	/** Terminal ID */
	terminal_id?: string;
	/** Transaction type used in schema */
	transaction_type?: string;
}
