/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsAdditionalServiceCostValueAdditionalServiceCostValue } from "./incustClientApiClientModelsAdditionalServiceCostValueAdditionalServiceCostValue";

/**
 * AdditionalServiceCost
 */
export interface IncustClientApiClientModelsAdditionalServiceCostAdditionalServiceCost {
	/** Currency ISO code */
	currency?: string;
	/** Number of decimal digits in value */
	decimal_digits?: number;
	/** Minimal value */
	min_value?: number;
	/** Threshold values */
	values?: IncustClientApiClientModelsAdditionalServiceCostValueAdditionalServiceCostValue[];
}
