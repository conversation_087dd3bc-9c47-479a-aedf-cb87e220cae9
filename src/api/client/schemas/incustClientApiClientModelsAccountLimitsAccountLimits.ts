/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * AccountLimits
 */
export interface IncustClientApiClientModelsAccountLimitsAccountLimits {
	/** Amount of funds on the account available for using (null if unlimited) */
	account_available_amount?: number;
	/** The time from which the funds in the account are available for use (null if no time limits) */
	available_from?: string;
	/** The time until which the funds in the account remain available for use (null if no time limits) */
	available_until?: string;
	/** Customer account day limit (if 0 - unlimited) */
	day_limit?: number;
	/** Customer account day available amount, not for unlimited account (null if unlimited) */
	day_limit_available?: number;
	/** Customer account day used amount */
	day_limit_used?: number;
	/** Customer account month limit (if 0 - unlimited) */
	month_limit?: number;
	/** Customer account month available amount, not for unlimited account (null if unlimited) */
	month_limit_available?: number;
	/** Customer account month used amount */
	month_limit_used?: number;
	/** Customer account overall limit (if 0 - unlimited) (optional) */
	overall_limit?: number;
	/** Customer account overall available amount, not for unlimited account (null if unlimited) (optional) */
	overall_limit_available?: number;
	/** Customer account overall used amount (optional) */
	overall_limit_used?: number;
}
