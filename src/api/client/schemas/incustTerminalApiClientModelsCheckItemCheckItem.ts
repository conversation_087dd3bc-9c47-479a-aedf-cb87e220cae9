/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsCheckItemAdditionalInfoCheckItemAdditionalInfo } from "./incustTerminalApiClientModelsCheckItemAdditionalInfoCheckItemAdditionalInfo";
import type { IncustTerminalApiClientModelsCheckMarginsCheckMargins } from "./incustTerminalApiClientModelsCheckMarginsCheckMargins";
import type { IncustTerminalApiClientModelsCheckTaxesCheckTaxes } from "./incustTerminalApiClientModelsCheckTaxesCheckTaxes";
import type { IncustTerminalApiClientModelsPriceMarginsPriceMargins } from "./incustTerminalApiClientModelsPriceMarginsPriceMargins";
import type { IncustTerminalApiClientModelsGoodsPassThruGoodsPassThru } from "./incustTerminalApiClientModelsGoodsPassThruGoodsPassThru";
import type { IncustTerminalApiClientModelsPriceTaxesPriceTaxes } from "./incustTerminalApiClientModelsPriceTaxesPriceTaxes";

/**
 * CheckItem
 */
export interface IncustTerminalApiClientModelsCheckItemCheckItem {
	additions?: IncustTerminalApiClientModelsCheckItemAdditionalInfoCheckItemAdditionalInfo;
	/** Item row total */
	amount?: number;
	/** Number of decimal digits in amount value */
	amount_decimal_digits?: number;
	/** Item row total by authorized payment transaction */
	authorized_amount?: number;
	/** Item quantity by authorized payment transaction */
	authorized_quantity?: number;
	/** Amount of added bonuses */
	bonuses_added?: number;
	/** Number of decimal digits in bonuses value */
	bonuses_decimal_digits?: number;
	/** Goods item total bonus points redeemed calculated by check (for list with stock) */
	calculated_bonuses_redeemed_amount?: number;
	/** Goods item total discount calculated by rules (for list with stock) */
	calculated_discount_amount?: number;
	calculated_margins?: IncustTerminalApiClientModelsCheckMarginsCheckMargins;
	/** Goods item price calculated by rules (for list with stock) */
	calculated_price?: number;
	calculated_taxes?: IncustTerminalApiClientModelsCheckTaxesCheckTaxes;
	/** Goods item unit bonus points redeemed calculated by check (for list with stock) */
	calculated_unit_bonuses_redeemed_amount?: number;
	/** Goods item unit discount calculated by rules (for list with stock) */
	calculated_unit_discount_amount?: number;
	/** item category */
	category?: string;
	/** item code */
	code: string;
	/** Amount of discount */
	discount_amount?: number;
	/** Loyalty item unique id related to check item (optional, readonly) */
	loyalty_item_id?: string;
	margins?: IncustTerminalApiClientModelsPriceMarginsPriceMargins;
	passthru_data?: IncustTerminalApiClientModelsGoodsPassThruGoodsPassThru;
	/** Item price */
	price?: number;
	/** Number of decimal digits in price value */
	price_decimal_digits?: number;
	/** Item row total by job */
	processed_amount?: number;
	/** Item quantity by job */
	processed_quantity?: number;
	/** Item quantity */
	quantity?: number;
	/** Number of decimal digits in quantity value (only for quantity_recalculate_type is applicable) */
	quantity_decimal_digits?: number;
	/** Quantity recalculate type (if needed) */
	quantity_recalculate_type?: string;
	/** Number of decimal digits in special account value */
	special_accounts_decimal_digits?: number;
	taxes?: IncustTerminalApiClientModelsPriceTaxesPriceTaxes;
	/** item title */
	title?: string;
	/** Measuring units */
	unit?: string;
	/** Weight goods flag */
	weighing?: number;
	/** Item quantity only by whole number flag */
	whole_number?: number;
}
