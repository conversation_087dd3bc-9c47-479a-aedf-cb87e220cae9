/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCouponBatchAliasCouponBatchAlias } from "./incustClientApiClientModelsCouponBatchAliasCouponBatchAlias";
import type { IncustClientApiClientModelsCouponBatchLinkCouponBatchLink } from "./incustClientApiClientModelsCouponBatchLinkCouponBatchLink";

/**
 * CouponBatch
 */
export interface IncustClientApiClientModelsCouponBatchCouponBatch {
	/** Coupons active flag */
	active?: number;
	alias?: IncustClientApiClientModelsCouponBatchAliasCouponBatchAlias;
	/** Business code */
	business_code?: string;
	/** Coupon batch code */
	code?: string;
	/** Coupon emission price (optional) */
	customers_price_amount?: number;
	/** Coupon emission price currency (optional) */
	customers_price_currency?: string;
	/** Coupon emission price special account Id (optional) */
	customers_price_special_account_id?: string;
	/** Coupon emission price special account title (optional) */
	customers_price_special_account_title?: string;
	/** Coupon emission price type (optional) */
	customers_price_type?: string;
	/** Coupons batch description */
	description?: string;
	/** Date of emission begins */
	emission_period_begin?: string;
	/** Date of emission ends */
	emission_period_end?: string;
	/** External code visibility type */
	external_code_visibility_type?: string;
	/** Unique id of coupon batch */
	id?: string;
	image?: string;
	links?: IncustClientApiClientModelsCouponBatchLinkCouponBatchLink[];
	/** Loyalty emission id */
	loyalty_id?: string;
	/** Coupons batch public description (optional, same as description) */
	public_description?: string;
	/** Coupons batch public title (optional, same as title) */
	public_title?: string;
	/** Recommendation fee amount (optional) */
	recommendation_fee_amount?: number;
	/** Recommendation fee bonuses currence ISO code (optional) */
	recommendation_fee_bonuses_currency?: string;
	/** Recommendation fee Batch CODE (optional) */
	recommendation_fee_coupon_batch_code?: string;
	/** Recommendation fee Batch Id (optional) */
	recommendation_fee_coupon_batch_id?: string;
	/** Recommendation fee Batch TITLE (optional) */
	recommendation_fee_coupon_batch_title?: string;
	/** Value of recommendation fee promotional bonuses expire date (optional) */
	recommendation_fee_promotional_bonuses_expire_date?: string;
	/** Recommendation fee expiration value type (optional) */
	recommendation_fee_promotional_bonuses_expire_type?: string;
	/** Value of recommendation fee promotional bonuses expire (optional) */
	recommendation_fee_promotional_bonuses_expire_value?: number;
	/** Recommendation fee special account Id (optional) */
	recommendation_fee_special_account_id?: string;
	/** Recommendation fee special account title (optional) */
	recommendation_fee_special_account_title?: string;
	/** Recommendation fee type */
	recommendation_fee_type?: string;
	/** Redeem only at terminal restriction */
	redeem_at_terminal?: number;
	/** Coupons share allowed for customer */
	share_allowed?: number;
	/** Coupons batch title */
	title?: string;
	/** Type of coupon */
	type?: string;
}
