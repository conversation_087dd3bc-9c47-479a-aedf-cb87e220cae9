/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsBillingInfoBillingInfoAddress } from "./incustClientApiClientModelsBillingInfoBillingInfoAddress";

/**
 * BillingInfo
 */
export interface IncustClientApiClientModelsBillingInfoBillingInfo {
	/** Address (depends on the payment system) */
	address?: IncustClientApiClientModelsBillingInfoBillingInfoAddress;
	/** Email */
	email?: string;
	/** Name */
	name?: string;
	/** Phone number */
	phone?: string;
}
