/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsFeedbackSourceFeedbackSource } from "./incustClientApiClientModelsFeedbackSourceFeedbackSource";

/**
 * CustomerFeedbackSettings
 */
export interface IncustClientApiClientModelsCustomerFeedbackSettingsCustomerFeedbackSettings {
	/** Feedback source active flag */
	active?: number;
	/** Allow unauthorized customers attach files to feedback */
	allow_attach_file_for_unauthorized_customer?: number;
	/** Allow unauthorized customers leave ratings */
	allow_rating_for_unauthorized_customer?: number;
	/** Allow unauthorized customers leave reviews */
	allow_review_for_unauthorized_customer?: number;
	/** Allow leave ratings/reviews */
	allowed?: string;
	/** Text displayed for customers as invitation */
	feedback_invitation_text?: string;
	/** Maximum allowable number of ratings/reviews from the customer to one feedback source (0 is unlimited). */
	feedback_per_customer_limit?: number;
	feedback_source?: IncustClientApiClientModelsFeedbackSourceFeedbackSource;
}
