/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsAdditionalServiceCostAdditionalServiceCost } from "./incustTerminalApiClientModelsAdditionalServiceCostAdditionalServiceCost";

/**
 * AdditionalService
 */
export interface IncustTerminalApiClientModelsAdditionalServiceAdditionalService {
	/** Service code */
	code?: string;
	/** Service cost */
	cost?: IncustTerminalApiClientModelsAdditionalServiceCostAdditionalServiceCost[];
	/** Service is default */
	default?: number;
	/** Additional service ID */
	id?: string;
	/** Service description */
	public_description?: string;
	/** Service title */
	public_title?: string;
	/** Sort order weight */
	sort_order?: number;
	/** Service type */
	type?: string;
}
