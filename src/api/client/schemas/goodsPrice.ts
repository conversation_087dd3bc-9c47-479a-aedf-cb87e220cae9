/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { CustomerPrice } from "./customerPrice";
import type { IncustClientApiClientModelsPriceMarginsPriceMargins } from "./incustClientApiClientModelsPriceMarginsPriceMargins";
import type { IncustClientApiClientModelsSpecialAccountSpecialAccount } from "./incustClientApiClientModelsSpecialAccountSpecialAccount";
import type { IncustClientApiClientModelsPriceTaxesPriceTaxes } from "./incustClientApiClientModelsPriceTaxesPriceTaxes";

/**
 * GoodsPrice
 */
export interface GoodsPrice {
	/** Is price applicable for customer/corporate customer (readonly) */
	applicable?: boolean;
	/** Price is applied only to identified customers, not to anonymous one */
	applicable_identified_customers_only?: boolean;
	/** Currensy ISO code */
	currency?: string;
	/** Prices available for customer */
	customer_prices?: CustomerPrice[];
	/** Price ID */
	id?: string;
	margins?: IncustClientApiClientModelsPriceMarginsPriceMargins;
	/** Value */
	price?: number;
	/** Number of decimal digits in price value */
	price_decimal_digits?: number;
	/** Price including taxes */
	price_including_taxes?: number;
	/** Posted (station level) price */
	price_posted?: number;
	/** Sale (check, receipt) price (optional) */
	price_sale?: number;
	special_account?: IncustClientApiClientModelsSpecialAccountSpecialAccount;
	/** Special account ID */
	special_account_id?: string;
	taxes?: IncustClientApiClientModelsPriceTaxesPriceTaxes;
	/** Type of price origin */
	type?: string;
}
