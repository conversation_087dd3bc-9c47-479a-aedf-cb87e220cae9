/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsCouponBatchAliasCouponBatchAlias } from "./incustTerminalApiClientModelsCouponBatchAliasCouponBatchAlias";
import type { IncustTerminalApiClientModelsCouponBatchLinkCouponBatchLink } from "./incustTerminalApiClientModelsCouponBatchLinkCouponBatchLink";

/**
 * CouponBatch
 */
export interface IncustTerminalApiClientModelsCouponBatchCouponBatch {
	alias?: IncustTerminalApiClientModelsCouponBatchAliasCouponBatchAlias;
	/** Automatically redeem on wallet add event (optional) */
	auto_redeem_on_add_to_wallet?: number;
	/** Business Id */
	business_id?: string;
	/** Coupon emission price (optional) */
	customers_price_amount?: number;
	/** Coupon emission price currency (optional) */
	customers_price_currency?: string;
	/** Coupon emission price special account Id (optional) */
	customers_price_special_account_id?: string;
	/** Coupon emission price special account title (optional) */
	customers_price_special_account_title?: string;
	/** Coupon emission price type (optional) */
	customers_price_type?: string;
	/** Unique id of coupon batch */
	id?: string;
	links?: IncustTerminalApiClientModelsCouponBatchLinkCouponBatchLink[];
	/** Loyalty emission id */
	loyalty_id?: string;
	/** Coupons batch public description (optional) */
	public_description?: string;
	/** Coupons batch public title (optional) */
	public_title?: string;
	/** Recommendation fee amount (optional) */
	recommendation_fee_amount?: number;
	/** Recommendation fee bonuses currence ISO code (optional) */
	recommendation_fee_bonuses_currency?: string;
	/** Recommendation fee Batch CODE (optional) */
	recommendation_fee_coupon_batch_code?: string;
	/** Recommendation fee Batch Id (optional) */
	recommendation_fee_coupon_batch_id?: string;
	/** Recommendation fee Batch TITLE (optional) */
	recommendation_fee_coupon_batch_title?: string;
	/** Value of recommendation fee promotional bonuses expire date (optional) */
	recommendation_fee_promotional_bonuses_expire_date?: string;
	/** Recommendation fee expiration value type (optional) */
	recommendation_fee_promotional_bonuses_expire_type?: string;
	/** Value of recommendation fee promotional bonuses expire (optional) */
	recommendation_fee_promotional_bonuses_expire_value?: number;
	/** Recommendation fee special account Id (optional) */
	recommendation_fee_special_account_id?: string;
	/** Recommendation fee special account title (optional) */
	recommendation_fee_special_account_title?: string;
	/** Recommendation fee type (optional) */
	recommendation_fee_type?: string;
	/** Redeem only at terminal restriction (optional) */
	redeem_at_terminal?: number;
	/** Coupons share allowed for customer (optional) */
	share_allowed?: number;
	/** Type of coupon */
	type?: string;
	/** URL of the coupon batch image */
	image?: string;
}
