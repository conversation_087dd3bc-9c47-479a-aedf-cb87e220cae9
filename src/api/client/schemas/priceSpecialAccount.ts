/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * PriceSpecialAccount
 */
export interface PriceSpecialAccount {
	/** Active flag */
	active?: number;
	/** Corporate accoiunt flag */
	corporate?: number;
	/** Credit limit (only for 'credit' accounts) */
	credit_limit?: number;
	/** Credit type (only for corporate accounts) */
	credit_type?: string;
	/** Currency ISO code */
	currency?: string;
	/** Account applicable goods */
	goods_items?: string[];
	/** Special account id */
	id?: string;
	/** Title to show */
	public_title?: string;
	/** Account title */
	title?: string;
	/** Account type */
	type?: string;
	/** The limit of redemption from customer account without an confirmation */
	unconfirmed_redeem_amount?: number;
}
