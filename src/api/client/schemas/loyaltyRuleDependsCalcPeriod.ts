/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * LoyaltyRuleDependsCalcPeriod
 */
export type LoyaltyRuleDependsCalcPeriod =
	(typeof LoyaltyRuleDependsCalcPeriod)[keyof typeof LoyaltyRuleDependsCalcPeriod];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LoyaltyRuleDependsCalcPeriod = {
	all: "all",
	check: "check",
	day: "day",
	week: "week",
	month: "month",
	"three-month": "three-month",
	year: "year",
	"current-week": "current-week",
	"past-week": "past-week",
	"current-month": "current-month",
	"past-month": "past-month",
	"current-quarter": "current-quarter",
	"past-quarter": "past-quarter",
	"current-year": "current-year",
	"past-year": "past-year",
} as const;
