/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * Loyalty bonuses and discount settings  # noqa: E501
 */
export interface LoyaltyBonusesSettings {
	/** Limit bonus points redeem in percentage of check amount */
	bonus_payment_limit?: number;
	/** Bonus points payment settings source (optional) */
	bonus_payment_settings_source?: string;
	/** Bonus points redeem type (only full check amount, partial check amount, partial check amount rounded to the nearest whole number) */
	bonus_payment_type?: string;
	/** Limit bonus points redeem amount without PIN confirmation */
	unconfirmed_bonus_payment_amount?: number;
	/** Limit discount amount without PIN confirmation */
	unconfirmed_discount_amount?: number;
}
