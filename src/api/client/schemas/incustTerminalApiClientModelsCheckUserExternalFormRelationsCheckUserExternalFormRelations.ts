/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsUserExternalFormUserExternalForm } from "./incustTerminalApiClientModelsUserExternalFormUserExternalForm";

/**
 * CheckUserExternalFormRelations
 */
export interface IncustTerminalApiClientModelsCheckUserExternalFormRelationsCheckUserExternalFormRelations {
	/** Requred user external forms */
	required?: IncustTerminalApiClientModelsUserExternalFormUserExternalForm[];
	/** Wanted user external forms */
	wanted?: IncustTerminalApiClientModelsUserExternalFormUserExternalForm[];
}
