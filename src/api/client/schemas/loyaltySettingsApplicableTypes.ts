/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * An enumeration.
 */
export type LoyaltySettingsApplicableTypes =
	(typeof LoyaltySettingsApplicableTypes)[keyof typeof LoyaltySettingsApplicableTypes];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LoyaltySettingsApplicableTypes = {
	for_participants: "for_participants",
	for_all: "for_all",
} as const;
