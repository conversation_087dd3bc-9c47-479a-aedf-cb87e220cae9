/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsJobEquipmentJobEquipment } from "./incustTerminalApiClientModelsJobEquipmentJobEquipment";
import type { IncustTerminalApiClientModelsJobFuelJobFuel } from "./incustTerminalApiClientModelsJobFuelJobFuel";

/**
 * JobDetails
 */
export interface IncustTerminalApiClientModelsJobDetailsJobDetails {
	equipment?: IncustTerminalApiClientModelsJobEquipmentJobEquipment;
	/** Webhook endpoint URL to receive Job finished status */
	finish_endpoint_url?: string;
	fuel?: IncustTerminalApiClientModelsJobFuelJobFuel;
}
