/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustClientApiClientModelsCheckCheck } from "./incustClientApiClientModelsCheckCheck";
import type { IncustClientApiClientModelsTransactionErrorsTransactionErrors } from "./incustClientApiClientModelsTransactionErrorsTransactionErrors";
import type { IncustClientApiClientModelsCheckItemCheckItem } from "./incustClientApiClientModelsCheckItemCheckItem";
import type { IncustClientApiClientModelsCheckServiceCheckService } from "./incustClientApiClientModelsCheckServiceCheckService";
import type { IncustClientApiClientModelsTransactionTypeTransactionType } from "./incustClientApiClientModelsTransactionTypeTransactionType";

/**
 * Transaction
 */
export interface IncustClientApiClientModelsTransactionTransaction {
	amount?: number;
	/** Number of decimal digits in amount value */
	amount_decimal_digits?: number;
	amount_to_pay?: number;
	bonuses_added?: number;
	/** Number of decimal digits in bonuses value */
	bonuses_decimal_digits?: number;
	bonuses_on_account?: number;
	bonuses_redeemed?: number;
	/** Transaction canceled */
	canceled?: boolean;
	/** Transaction canceled */
	cancelled?: number;
	check?: IncustClientApiClientModelsCheckCheck;
	/** Corporate customer email */
	corporate_customer_email?: string;
	/** Corporate customer name */
	corporate_customer_name?: string;
	/** Currency ISO code (if transaction payment 'currency') */
	currency?: string;
	/** POS description */
	description?: string;
	/** Transaction discount amount */
	discount_amount?: number;
	/** Errors list */
	errors?: IncustClientApiClientModelsTransactionErrorsTransactionErrors[];
	/** Finalize Transaction ID */
	finalize_transaction_id?: string;
	/** Transaction finalized */
	finalized?: boolean;
	/** Transaction ID */
	id?: string;
	/** Transaction items (payment info only) */
	items?: IncustClientApiClientModelsCheckItemCheckItem[];
	/** Online store order ID */
	online_store_order_id?: string;
	/** Payment type */
	payment_type?: string;
	/** Transaction UTC processed date-time */
	processed?: string;
	/** Transaction local (by POS time zone) processed date-time */
	processed_local?: string;
	/** Transaction services (payment info only) */
	services?: IncustClientApiClientModelsCheckServiceCheckService[];
	services_amount?: number;
	shipping_amount?: number;
	/** Special account currency ISO code */
	special_account_currency?: string;
	/** Special account ID (if transaction payment in 'special-account', 'corporate-special-account-access', 'retail-special-account-access') */
	special_account_id?: string;
	/** Special account title */
	special_account_title?: string;
	/** Special account type */
	special_account_type?: string;
	/** Number of decimal digits in special account value */
	special_accounts_decimal_digits?: number;
	subtype?: IncustClientApiClientModelsTransactionTypeTransactionType;
	/** Sum of amount and shipping_amount and services_amount */
	summary_amount?: number;
	/** POS title */
	title?: string;
	type?: IncustClientApiClientModelsTransactionTypeTransactionType;
}
