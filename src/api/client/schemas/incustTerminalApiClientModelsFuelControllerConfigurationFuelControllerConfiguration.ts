/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits } from "./incustTerminalApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits";
import type { IncustTerminalApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits } from "./incustTerminalApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits";

/**
 * FuelControllerConfiguration
 */
export interface IncustTerminalApiClientModelsFuelControllerConfigurationFuelControllerConfiguration {
	decimal_digits?: IncustTerminalApiClientModelsFuelControllerConfigurationDecimalDigitsFuelControllerConfigurationDecimalDigits;
	measurement_units?: IncustTerminalApiClientModelsFuelControllerConfigurationMeasurementUnitsFuelControllerConfigurationMeasurementUnits;
}
