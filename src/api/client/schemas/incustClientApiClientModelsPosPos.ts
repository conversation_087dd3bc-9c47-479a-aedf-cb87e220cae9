/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { AccountsRec } from "./accountsRec";
import type { IncustClientApiClientModelsAdditionalServiceAdditionalService } from "./incustClientApiClientModelsAdditionalServiceAdditionalService";
import type { BonusesRec } from "./bonusesRec";
import type { IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory } from "./incustClientApiClientModelsLoyaltyCategoryLoyaltyCategory";
import type { IncustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess } from "./incustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess";
import type { IncustClientApiClientModelsCouponCoupon } from "./incustClientApiClientModelsCouponCoupon";
import type { EquipmentRec } from "./equipmentRec";
import type { FuelRec } from "./fuelRec";
import type { LoyaltyBonusesSettings } from "./loyaltyBonusesSettings";
import type { OnlineStore } from "./onlineStore";
import type { PaymentSystemsRec } from "./paymentSystemsRec";
import type { IncustClientApiClientModelsPhoneNumberPhoneNumber } from "./incustClientApiClientModelsPhoneNumberPhoneNumber";
import type { IncustClientApiClientModelsImageImage } from "./incustClientApiClientModelsImageImage";
import type { ReferralProgram } from "./referralProgram";

/**
 * Point of sale  # noqa: E501
 */
export interface IncustClientApiClientModelsPosPos {
	/** Special account info */
	accounts?: AccountsRec[];
	/** Additional services (optional) */
	additional_services?: IncustClientApiClientModelsAdditionalServiceAdditionalService[];
	/** Is additional services required */
	additional_services_required?: number;
	/** Address of sale point */
	addresses?: string;
	/** The basic bonuses */
	bonuses?: BonusesRec[];
	/** Pos catalog publication state */
	catalog_publication_state?: string;
	/** Categories */
	categories?: IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory[];
	/** Pos check items entering mode possibilities */
	check_item_enter_mode?: string;
	/** List of access to the corporate customer special accounts */
	corporate_special_accounts_access?: IncustClientApiClientModelsCorporateCustomerSpecialAccountAccessCorporateCustomerSpecialAccountAccess[];
	/** Country code */
	country?: string;
	/** Coupons info */
	coupons?: IncustClientApiClientModelsCouponCoupon[];
	/** Point of sale public description */
	description?: string;
	equipments?: EquipmentRec[];
	/** Country code */
	favorite?: boolean;
	fuel?: FuelRec;
	/** Point of sales ID */
	id?: string;
	/** GEO Latitude */
	latitude?: number;
	/** Loyalty legal info */
	legal_info?: string;
	links?: IncustClientApiClientModelsLoyaltyCategoryLoyaltyCategory;
	/** GEO Longitude */
	longitude?: number;
	loyalty_bonuses_settings?: LoyaltyBonusesSettings;
	/** Loyalty ID */
	loyalty_id?: string;
	/** Maximum service distance in km (0 - unlimited) */
	maximum_service_distance?: number;
	online_store?: OnlineStore;
	payment_systems_availability?: PaymentSystemsRec;
	/** Phones of sale point */
	phones?: IncustClientApiClientModelsPhoneNumberPhoneNumber[];
	photos?: IncustClientApiClientModelsImageImage[];
	/** Loyalty privacy policy link */
	privacy_policy_url?: string;
	/** Point of sale public description (same as description) */
	public_description?: string;
	/** Point of sale public title (same as title) */
	public_title?: string;
	referral_program?: ReferralProgram;
	/** Time zone */
	time_zone?: string;
	/** Point of sale public title */
	title?: string;
	/** Pos type */
	type?: string;
}
