/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * CheckTank
 */
export interface IncustClientApiClientModelsCheckTankCheckTank {
	/** Title */
	address?: string;
	/** Tank unique code (read only) */
	code?: string;
	/** Linked product code (optional, only for linked tank data) (read only) */
	goods_code?: string;
	/** Latitude in decimal degrees */
	latitude?: number;
	/** Longitude in decimal degrees */
	longitude?: number;
	/** Physical DIP Amount */
	physical_dip_amount?: number;
	/** Tank product volume after operation (read only) */
	product_volume_after?: number;
	/** Tank product volume before operation (read only) */
	product_volume_before?: number;
	/** Tank id */
	tank_id?: string;
	/** Title (read only) */
	title?: string;
	/** Tank volume (read only) */
	volume?: number;
}
