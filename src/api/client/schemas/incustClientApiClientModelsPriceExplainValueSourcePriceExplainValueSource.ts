/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * PriceExplainValueSource
 */
export type IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource =
	(typeof IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource)[keyof typeof IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IncustClientApiClientModelsPriceExplainValueSourcePriceExplainValueSource = {
	defined: "defined",
	received: "received",
	calculated: "calculated",
	reseted: "reseted",
	"price-by-item": "price-by-item",
	"price-by-special-account": "price-by-special-account",
	"master-price-by-date": "master-price-by-date",
	"price-group-tax-by-item": "price-group-tax-by-item",
	"price-group-tax-default": "price-group-tax-default",
	"price-group-tax-default-base": "price-group-tax-default-base",
	"terminal-additional-cost-by-item": "terminal-additional-cost-by-item",
	"terminal-additional-cost-default": "terminal-additional-cost-default",
	"terminal-additional-cost-default-base": "terminal-additional-cost-default-base",
	"corporate-account-price-option-by-item": "corporate-account-price-option-by-item",
	"corporate-category-account-price-option-by-item":
		"corporate-category-account-price-option-by-item",
	"retail-category-account-price-option-by-item": "retail-category-account-price-option-by-item",
	"retail-category-currency-price-option-by-item":
		"retail-category-currency-price-option-by-item",
} as const;
