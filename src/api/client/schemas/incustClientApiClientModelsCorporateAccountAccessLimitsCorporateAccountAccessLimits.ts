/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */

/**
 * CorporateAccountAccessLimits
 */
export interface IncustClientApiClientModelsCorporateAccountAccessLimitsCorporateAccountAccessLimits {
	/** Amount of funds on the account available for using (null if unlimited) */
	account_available_amount?: number;
	/** The time from which the funds in the account are available for use (null if no time limits) */
	account_available_from?: string;
	/** The time until which the funds in the account remain available for use (null if no time limits) */
	account_available_until?: string;
	/** Corporate customer account day limit (if 0 - unlimited) */
	account_day_limit?: number;
	/** Corporate customer account day available amount, not for unlimited account (null if unlimited) */
	account_day_limit_available?: number;
	/** Corporate customer account day used amount */
	account_day_limit_used?: number;
	/** Corporate customer account month limit (if 0 - unlimited) */
	account_month_limit?: number;
	/** Corporate customer account month available amount, not for unlimited account (null if unlimited) */
	account_month_limit_available?: number;
	/** Corporate customer account month used amount */
	account_month_limit_used?: number;
	/** The time from which the funds in the account are available for use (null if no time limits) */
	user_available_from?: string;
	/** The time until which the funds in the account remain available for use (null if no time limits) */
	user_available_until?: string;
	/** User access to the account day limit (if 0 - unlimited) */
	user_day_limit?: number;
	/** User access to the account day available amount, not for unlimited account (null if unlimited) */
	user_day_limit_available?: number;
	/** User access to the account day used amount */
	user_day_limit_used?: number;
	/** User access to the account month limit (if 0 - unlimited) */
	user_month_limit?: number;
	/** User access to the account month available amount, not for unlimited account (null if unlimited) */
	user_month_limit_available?: number;
	/** User access to the account month used amount */
	user_month_limit_used?: number;
	/** User access to the account overall limit (if 0 - unlimited) */
	user_overall_limit?: number;
	/** User access to the account overall available amount, not for unlimited account (null if unlimited) */
	user_overall_limit_available?: number;
	/** User access to the account overall used amount */
	user_overall_limit_used?: number;
}
