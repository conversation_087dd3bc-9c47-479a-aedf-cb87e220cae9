/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import type { IncustTerminalApiClientModelsJobJob } from "./incustTerminalApiClientModelsJobJob";

/**
 * CheckItemAdditionalInfo
 */
export interface IncustTerminalApiClientModelsCheckItemAdditionalInfoCheckItemAdditionalInfo {
	job?: IncustTerminalApiClientModelsJobJob;
	/** Loyalty item unique id related to check item (optional, readonly) */
	loyalty_item_id?: string;
	/** Check item processing id (optional, readonly) */
	processing_id?: string;
	/** Number of decimal digits in quantity value (optional, readonly) */
	quantity_decimal_digits?: number;
	/** Quantity recalculate skipped flag (optional, readonly) */
	quantity_recalculate_skipped?: boolean;
	/** Quantity recalculate type (optional, readonly) */
	quantity_recalculate_type?: string;
}
