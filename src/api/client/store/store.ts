/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import {
	useInfiniteQuery,
	useMutation,
	useQuery,
	useSuspenseInfiniteQuery,
	useSuspenseQuery,
} from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseInfiniteQueryResult,
	DefinedUseQueryResult,
	InfiniteData,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseInfiniteQueryOptions,
	UseInfiniteQueryResult,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseInfiniteQueryOptions,
	UseSuspenseInfiniteQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	AddCartProduct,
	AddProductToCartHeaders,
	AvailablePaymentSchema,
	BaseSimpleTextModel,
	BrandSchema,
	CancelStoreOrderHeaders,
	CartCreated,
	CartProductResponseSchema,
	CartSchema,
	CategorySchema,
	CharacteristicSchema,
	CharacteristicValues,
	CheckInstockCartHeaders,
	CheckInstockCartParams,
	CheckInstockOrderHeaders,
	CitiesResponse,
	ClearCartData,
	ClearCartHeaders,
	CreateCartHeaders,
	CreateFiltersData,
	CreateFiltersHeaders,
	CreateOrderHeaders,
	CreateOrderInStoreHeaders,
	CreateOrderInStoreSchema,
	CreateOrderSchema,
	CustomShipmentsSchema,
	DeleteCartHeaders,
	DeleteCartParams,
	DeleteProductFromCartHeaders,
	DeleteProductFromCartParams,
	DetectBrandHeaders,
	DetectBrandParams,
	DetectedBrandSchema,
	ExtraFeeSchema,
	FavoritesSchema,
	FiltersCreated,
	FindProductModificationData,
	GetAvailablePaymentsHeaders,
	GetAvailablePaymentsParams,
	GetBrandHeaders,
	GetBrandsHeaders,
	GetCalculatedExtraFeeHeaders,
	GetCalculatedExtraFeeParams,
	GetCartHeaders,
	GetCartParams,
	GetCategoriesHeaders,
	GetCategoriesParams,
	GetCitiesHeaders,
	GetCitiesParams,
	GetCustomShipmentsHeaders,
	GetCustomShipmentsParams,
	GetDocumentHeaders,
	GetDocumentParams,
	GetFavoritesHeaders,
	GetFilterValuesHeaders,
	GetFilterValuesParams,
	GetIncustPayDataHeaders,
	GetIncustPayDataParams,
	GetMinMaxPricesHeaders,
	GetMinMaxPricesParams,
	GetOrderHeaders,
	GetOrderHistoryStatusesHeaders,
	GetOrderPaymentStatusHeaders,
	GetPaymentStatusHeaders,
	GetPaymentStatusParams,
	GetProductHeaders,
	GetProductModificationHeaders,
	GetProductsHeaders,
	GetProductsParams,
	GetReceiptHeaders,
	GetReceiptParams,
	GetShipmentPriceHeaders,
	GetShipmentPriceParams,
	GetShipmentPricesHeaders,
	GetShipmentPricesParams,
	GetShipmentsHeaders,
	GetShipmentsParams,
	GetStoreByCoordinatesOrAddressHeaders,
	GetStoreByCoordinatesOrAddressParams,
	GetStoreFiltersHeaders,
	GetStoreFiltersParams,
	GetStoreHeaders,
	GetStores200,
	GetStoresHeaders,
	GetStoresParams,
	GetTreeCategoriesHeaders,
	GetTreeCategoriesParams,
	GetUserOrders200,
	GetUserOrdersHeaders,
	GetUserOrdersParams,
	HTTPValidationError,
	IncustPayPaymentData,
	NotAvailableProduct,
	OrderHistorySchema,
	OrderID,
	OrderPaymentStatusSchema,
	OrderSchema,
	PaymentStatusSchema,
	ProductSchema,
	ProductsListResponse,
	ProductsMinMaxPrices,
	ReceiptSchema,
	SaveCartSchema,
	ScanAndSaveReceiptHeaders,
	ScanPayloadSchema,
	ScanReceiptHeaders,
	ScanResponseSchema,
	ScanResponseUnAuthSchema,
	SendOrderInvoiceToBotHeaders,
	SendOrderInvoiceToBotParams,
	SendTextNotificationData,
	SendTextNotificationHeaders,
	ShipmentPrice,
	ShipmentPriceResultData,
	ShipmentsData,
	StoreSchema,
	SyncCartSchema,
	SyncUsersCartHeaders,
	ToggleFavoriteProductResult,
	ToggleProductFavoriteHeaders,
	UpdateCartProductRequestSchema,
	UpdateOrderSchema,
	UpdateProductInCartHeaders,
	UpdateProductInCartParams,
	UpdateStoreOrderHeaders,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * Returns user cart with related products
 * @summary Get Cart
 */
export const getCart = (params?: GetCartParams, headers?: GetCartHeaders, signal?: AbortSignal) => {
	return getAxios<CartSchema>({ url: `/store/cart/`, method: "GET", headers, params, signal });
};

export const getGetCartQueryKey = (params?: GetCartParams) => {
	return [`/store/cart/`, ...(params ? [params] : [])] as const;
};

export const getGetCartQueryOptions = <
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCartQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCart>>> = ({ signal }) =>
		getCart(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getCart>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCartQueryResult = NonNullable<Awaited<ReturnType<typeof getCart>>>;
export type GetCartQueryError = ErrorType<HTTPValidationError>;

export function useGetCart<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCartParams,
	headers: undefined | GetCartHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCart>>,
					TError,
					Awaited<ReturnType<typeof getCart>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCart<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCart>>,
					TError,
					Awaited<ReturnType<typeof getCart>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCart<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Cart
 */

export function useGetCart<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCartQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCartSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCartQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCart>>> = ({ signal }) =>
		getCart(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCart>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCartSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getCart>>>;
export type GetCartSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCartSuspense<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCartParams,
	headers: undefined | GetCartHeaders,
	options: {
		query: Partial<UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCartSuspense<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCartSuspense<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Cart
 */

export function useGetCartSuspense<
	TData = Awaited<ReturnType<typeof getCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCartParams,
	headers?: GetCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCartSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to sync user cart with user cart
 * @summary Sync Users Cart
 */
export const syncUsersCart = (
	syncCartSchema: SyncCartSchema,
	headers?: SyncUsersCartHeaders,
	signal?: AbortSignal
) => {
	return getAxios<unknown>({
		url: `/store/cart/sync_users_cart`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: syncCartSchema,
		signal,
	});
};

export const getSyncUsersCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof syncUsersCart>>,
		TError,
		{ data: SyncCartSchema; headers?: SyncUsersCartHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof syncUsersCart>>,
	TError,
	{ data: SyncCartSchema; headers?: SyncUsersCartHeaders },
	TContext
> => {
	const mutationKey = ["syncUsersCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof syncUsersCart>>,
		{ data: SyncCartSchema; headers?: SyncUsersCartHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return syncUsersCart(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type SyncUsersCartMutationResult = NonNullable<Awaited<ReturnType<typeof syncUsersCart>>>;
export type SyncUsersCartMutationBody = SyncCartSchema;
export type SyncUsersCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Sync Users Cart
 */
export const useSyncUsersCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof syncUsersCart>>,
		TError,
		{ data: SyncCartSchema; headers?: SyncUsersCartHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof syncUsersCart>>,
	TError,
	{ data: SyncCartSchema; headers?: SyncUsersCartHeaders },
	TContext
> => {
	const mutationOptions = getSyncUsersCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method create user cart
 * @summary Create Cart
 */
export const createCart = (
	saveCartSchema: SaveCartSchema,
	headers?: CreateCartHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CartCreated>({
		url: `/store/cart/create`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: saveCartSchema,
		signal,
	});
};

export const getCreateCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createCart>>,
		TError,
		{ data: SaveCartSchema; headers?: CreateCartHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createCart>>,
	TError,
	{ data: SaveCartSchema; headers?: CreateCartHeaders },
	TContext
> => {
	const mutationKey = ["createCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createCart>>,
		{ data: SaveCartSchema; headers?: CreateCartHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return createCart(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateCartMutationResult = NonNullable<Awaited<ReturnType<typeof createCart>>>;
export type CreateCartMutationBody = SaveCartSchema;
export type CreateCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create Cart
 */
export const useCreateCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createCart>>,
		TError,
		{ data: SaveCartSchema; headers?: CreateCartHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createCart>>,
	TError,
	{ data: SaveCartSchema; headers?: CreateCartHeaders },
	TContext
> => {
	const mutationOptions = getCreateCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method delete user cart
 * @summary Delete Cart
 */
export const deleteCart = (params?: DeleteCartParams, headers?: DeleteCartHeaders) => {
	return getAxios<unknown>({ url: `/store/cart/delete`, method: "DELETE", headers, params });
};

export const getDeleteCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteCart>>,
		TError,
		{ params?: DeleteCartParams; headers?: DeleteCartHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof deleteCart>>,
	TError,
	{ params?: DeleteCartParams; headers?: DeleteCartHeaders },
	TContext
> => {
	const mutationKey = ["deleteCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteCart>>,
		{ params?: DeleteCartParams; headers?: DeleteCartHeaders }
	> = props => {
		const { params, headers } = props ?? {};

		return deleteCart(params, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteCartMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCart>>>;

export type DeleteCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Delete Cart
 */
export const useDeleteCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteCart>>,
		TError,
		{ params?: DeleteCartParams; headers?: DeleteCartHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof deleteCart>>,
	TError,
	{ params?: DeleteCartParams; headers?: DeleteCartHeaders },
	TContext
> => {
	const mutationOptions = getDeleteCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method for clear cart
 * @summary Clear Cart
 */
export const clearCart = (
	clearCartData: ClearCartData,
	headers?: ClearCartHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CartSchema>({
		url: `/store/cart/clear`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: clearCartData,
		signal,
	});
};

export const getClearCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof clearCart>>,
		TError,
		{ data: ClearCartData; headers?: ClearCartHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof clearCart>>,
	TError,
	{ data: ClearCartData; headers?: ClearCartHeaders },
	TContext
> => {
	const mutationKey = ["clearCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof clearCart>>,
		{ data: ClearCartData; headers?: ClearCartHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return clearCart(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ClearCartMutationResult = NonNullable<Awaited<ReturnType<typeof clearCart>>>;
export type ClearCartMutationBody = ClearCartData;
export type ClearCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Clear Cart
 */
export const useClearCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof clearCart>>,
		TError,
		{ data: ClearCartData; headers?: ClearCartHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof clearCart>>,
	TError,
	{ data: ClearCartData; headers?: ClearCartHeaders },
	TContext
> => {
	const mutationOptions = getClearCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method add product to user cart
 * @summary Add Product To Cart
 */
export const addProductToCart = (
	addCartProduct: AddCartProduct,
	headers?: AddProductToCartHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CartProductResponseSchema>({
		url: `/store/cart/products/add`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: addCartProduct,
		signal,
	});
};

export const getAddProductToCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addProductToCart>>,
		TError,
		{ data: AddCartProduct; headers?: AddProductToCartHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof addProductToCart>>,
	TError,
	{ data: AddCartProduct; headers?: AddProductToCartHeaders },
	TContext
> => {
	const mutationKey = ["addProductToCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof addProductToCart>>,
		{ data: AddCartProduct; headers?: AddProductToCartHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return addProductToCart(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type AddProductToCartMutationResult = NonNullable<
	Awaited<ReturnType<typeof addProductToCart>>
>;
export type AddProductToCartMutationBody = AddCartProduct;
export type AddProductToCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Add Product To Cart
 */
export const useAddProductToCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addProductToCart>>,
		TError,
		{ data: AddCartProduct; headers?: AddProductToCartHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof addProductToCart>>,
	TError,
	{ data: AddCartProduct; headers?: AddProductToCartHeaders },
	TContext
> => {
	const mutationOptions = getAddProductToCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method delete product from user cart
 * @summary Delete Product From Cart
 */
export const deleteProductFromCart = (
	cartProductOrProductId: number | undefined | null,
	params?: DeleteProductFromCartParams,
	headers?: DeleteProductFromCartHeaders
) => {
	return getAxios<CartSchema>({
		url: `/store/cart/products/${cartProductOrProductId}`,
		method: "DELETE",
		headers,
		params,
	});
};

export const getDeleteProductFromCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteProductFromCart>>,
		TError,
		{
			cartProductOrProductId: number | undefined | null;
			params?: DeleteProductFromCartParams;
			headers?: DeleteProductFromCartHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof deleteProductFromCart>>,
	TError,
	{
		cartProductOrProductId: number | undefined | null;
		params?: DeleteProductFromCartParams;
		headers?: DeleteProductFromCartHeaders;
	},
	TContext
> => {
	const mutationKey = ["deleteProductFromCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteProductFromCart>>,
		{
			cartProductOrProductId: number | undefined | null;
			params?: DeleteProductFromCartParams;
			headers?: DeleteProductFromCartHeaders;
		}
	> = props => {
		const { cartProductOrProductId, params, headers } = props ?? {};

		return deleteProductFromCart(cartProductOrProductId, params, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteProductFromCartMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteProductFromCart>>
>;

export type DeleteProductFromCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Delete Product From Cart
 */
export const useDeleteProductFromCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteProductFromCart>>,
		TError,
		{
			cartProductOrProductId: number | undefined | null;
			params?: DeleteProductFromCartParams;
			headers?: DeleteProductFromCartHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof deleteProductFromCart>>,
	TError,
	{
		cartProductOrProductId: number | undefined | null;
		params?: DeleteProductFromCartParams;
		headers?: DeleteProductFromCartHeaders;
	},
	TContext
> => {
	const mutationOptions = getDeleteProductFromCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to update product in cart
 * @summary Update Product In Cart
 */
export const updateProductInCart = (
	cartProductId: number | undefined | null,
	updateCartProductRequestSchema: UpdateCartProductRequestSchema,
	params?: UpdateProductInCartParams,
	headers?: UpdateProductInCartHeaders
) => {
	return getAxios<CartSchema>({
		url: `/store/cart/products/${cartProductId}`,
		method: "PATCH",
		headers: { "Content-Type": "application/json", ...headers },
		data: updateCartProductRequestSchema,
		params,
	});
};

export const getUpdateProductInCartMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateProductInCart>>,
		TError,
		{
			cartProductId: number | undefined | null;
			data: UpdateCartProductRequestSchema;
			params?: UpdateProductInCartParams;
			headers?: UpdateProductInCartHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof updateProductInCart>>,
	TError,
	{
		cartProductId: number | undefined | null;
		data: UpdateCartProductRequestSchema;
		params?: UpdateProductInCartParams;
		headers?: UpdateProductInCartHeaders;
	},
	TContext
> => {
	const mutationKey = ["updateProductInCart"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof updateProductInCart>>,
		{
			cartProductId: number | undefined | null;
			data: UpdateCartProductRequestSchema;
			params?: UpdateProductInCartParams;
			headers?: UpdateProductInCartHeaders;
		}
	> = props => {
		const { cartProductId, data, params, headers } = props ?? {};

		return updateProductInCart(cartProductId, data, params, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type UpdateProductInCartMutationResult = NonNullable<
	Awaited<ReturnType<typeof updateProductInCart>>
>;
export type UpdateProductInCartMutationBody = UpdateCartProductRequestSchema;
export type UpdateProductInCartMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Update Product In Cart
 */
export const useUpdateProductInCart = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateProductInCart>>,
		TError,
		{
			cartProductId: number | undefined | null;
			data: UpdateCartProductRequestSchema;
			params?: UpdateProductInCartParams;
			headers?: UpdateProductInCartHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof updateProductInCart>>,
	TError,
	{
		cartProductId: number | undefined | null;
		data: UpdateCartProductRequestSchema;
		params?: UpdateProductInCartParams;
		headers?: UpdateProductInCartHeaders;
	},
	TContext
> => {
	const mutationOptions = getUpdateProductInCartMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to check products in poster from cart
 * @summary Check Instock Cart
 */
export const checkInstockCart = (
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	signal?: AbortSignal
) => {
	return getAxios<NotAvailableProduct[]>({
		url: `/store/check_instock/cart`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getCheckInstockCartQueryKey = (params?: CheckInstockCartParams) => {
	return [`/store/check_instock/cart`, ...(params ? [params] : [])] as const;
};

export const getCheckInstockCartQueryOptions = <
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckInstockCartQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkInstockCart>>> = ({ signal }) =>
		checkInstockCart(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkInstockCart>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckInstockCartQueryResult = NonNullable<Awaited<ReturnType<typeof checkInstockCart>>>;
export type CheckInstockCartQueryError = ErrorType<HTTPValidationError>;

export function useCheckInstockCart<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | CheckInstockCartParams,
	headers: undefined | CheckInstockCartHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkInstockCart>>,
					TError,
					Awaited<ReturnType<typeof checkInstockCart>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockCart<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkInstockCart>>,
					TError,
					Awaited<ReturnType<typeof checkInstockCart>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockCart<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Instock Cart
 */

export function useCheckInstockCart<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckInstockCartQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckInstockCartSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckInstockCartQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkInstockCart>>> = ({ signal }) =>
		checkInstockCart(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkInstockCart>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckInstockCartSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkInstockCart>>
>;
export type CheckInstockCartSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useCheckInstockCartSuspense<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | CheckInstockCartParams,
	headers: undefined | CheckInstockCartHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockCartSuspense<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockCartSuspense<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Instock Cart
 */

export function useCheckInstockCartSuspense<
	TData = Awaited<ReturnType<typeof checkInstockCart>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: CheckInstockCartParams,
	headers?: CheckInstockCartHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockCart>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckInstockCartSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to check products in poster from order
 * @summary Check Instock Order
 */
export const checkInstockOrder = (
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	signal?: AbortSignal
) => {
	return getAxios<NotAvailableProduct[]>({
		url: `/store/check_instock/order/${orderId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getCheckInstockOrderQueryKey = (orderId: number | undefined | null) => {
	return [`/store/check_instock/order/${orderId}`] as const;
};

export const getCheckInstockOrderQueryOptions = <
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckInstockOrderQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkInstockOrder>>> = ({ signal }) =>
		checkInstockOrder(orderId, headers, signal);

	return { queryKey, queryFn, enabled: !!orderId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkInstockOrder>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckInstockOrderQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkInstockOrder>>
>;
export type CheckInstockOrderQueryError = ErrorType<HTTPValidationError>;

export function useCheckInstockOrder<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | CheckInstockOrderHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkInstockOrder>>,
					TError,
					Awaited<ReturnType<typeof checkInstockOrder>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockOrder<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkInstockOrder>>,
					TError,
					Awaited<ReturnType<typeof checkInstockOrder>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockOrder<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Instock Order
 */

export function useCheckInstockOrder<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckInstockOrderQueryOptions(orderId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckInstockOrderSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckInstockOrderQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkInstockOrder>>> = ({ signal }) =>
		checkInstockOrder(orderId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkInstockOrder>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckInstockOrderSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof checkInstockOrder>>
>;
export type CheckInstockOrderSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useCheckInstockOrderSuspense<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | CheckInstockOrderHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockOrderSuspense<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckInstockOrderSuspense<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Instock Order
 */

export function useCheckInstockOrderSuspense<
	TData = Awaited<ReturnType<typeof checkInstockOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: CheckInstockOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkInstockOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckInstockOrderSuspenseQueryOptions(orderId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Getting all brands
 * @summary Get Brands
 */
export const getBrands = (headers?: GetBrandsHeaders, signal?: AbortSignal) => {
	return getAxios<BrandSchema[]>({ url: `/store/brands/`, method: "GET", headers, signal });
};

export const getGetBrandsQueryKey = () => {
	return [`/store/brands/`] as const;
};

export const getGetBrandsQueryOptions = <
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBrandsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBrands>>> = ({ signal }) =>
		getBrands(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getBrands>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetBrandsQueryResult = NonNullable<Awaited<ReturnType<typeof getBrands>>>;
export type GetBrandsQueryError = ErrorType<HTTPValidationError>;

export function useGetBrands<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetBrandsHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getBrands>>,
					TError,
					Awaited<ReturnType<typeof getBrands>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrands<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getBrands>>,
					TError,
					Awaited<ReturnType<typeof getBrands>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrands<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Brands
 */

export function useGetBrands<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetBrandsQueryOptions(headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetBrandsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBrandsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBrands>>> = ({ signal }) =>
		getBrands(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getBrands>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetBrandsSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getBrands>>>;
export type GetBrandsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetBrandsSuspense<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetBrandsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrandsSuspense<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrandsSuspense<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Brands
 */

export function useGetBrandsSuspense<
	TData = Awaited<ReturnType<typeof getBrands>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetBrandsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrands>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetBrandsSuspenseQueryOptions(headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Detect Brand
 */
export const detectBrand = (
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	signal?: AbortSignal
) => {
	return getAxios<DetectedBrandSchema>({
		url: `/store/brands/detect`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getDetectBrandQueryKey = (params?: DetectBrandParams) => {
	return [`/store/brands/detect`, ...(params ? [params] : [])] as const;
};

export const getDetectBrandQueryOptions = <
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getDetectBrandQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof detectBrand>>> = ({ signal }) =>
		detectBrand(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof detectBrand>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type DetectBrandQueryResult = NonNullable<Awaited<ReturnType<typeof detectBrand>>>;
export type DetectBrandQueryError = ErrorType<HTTPValidationError>;

export function useDetectBrand<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | DetectBrandParams,
	headers: undefined | DetectBrandHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof detectBrand>>,
					TError,
					Awaited<ReturnType<typeof detectBrand>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useDetectBrand<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof detectBrand>>,
					TError,
					Awaited<ReturnType<typeof detectBrand>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useDetectBrand<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Detect Brand
 */

export function useDetectBrand<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getDetectBrandQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getDetectBrandSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getDetectBrandQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof detectBrand>>> = ({ signal }) =>
		detectBrand(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof detectBrand>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type DetectBrandSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof detectBrand>>>;
export type DetectBrandSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useDetectBrandSuspense<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | DetectBrandParams,
	headers: undefined | DetectBrandHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useDetectBrandSuspense<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useDetectBrandSuspense<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Detect Brand
 */

export function useDetectBrandSuspense<
	TData = Awaited<ReturnType<typeof detectBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: DetectBrandParams,
	headers?: DetectBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof detectBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getDetectBrandSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns store brand
 * @summary Get Brand
 */
export const getBrand = (
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	signal?: AbortSignal
) => {
	return getAxios<BrandSchema>({
		url: `/store/brands/${brandId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetBrandQueryKey = (brandId: number | undefined | null) => {
	return [`/store/brands/${brandId}`] as const;
};

export const getGetBrandQueryOptions = <
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBrandQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBrand>>> = ({ signal }) =>
		getBrand(brandId, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getBrand>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetBrandQueryResult = NonNullable<Awaited<ReturnType<typeof getBrand>>>;
export type GetBrandQueryError = ErrorType<HTTPValidationError>;

export function useGetBrand<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetBrandHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getBrand>>,
					TError,
					Awaited<ReturnType<typeof getBrand>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrand<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getBrand>>,
					TError,
					Awaited<ReturnType<typeof getBrand>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrand<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Brand
 */

export function useGetBrand<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetBrandQueryOptions(brandId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetBrandSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBrandQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBrand>>> = ({ signal }) =>
		getBrand(brandId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getBrand>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetBrandSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getBrand>>>;
export type GetBrandSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetBrandSuspense<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetBrandHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrandSuspense<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetBrandSuspense<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Brand
 */

export function useGetBrandSuspense<
	TData = Awaited<ReturnType<typeof getBrand>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetBrandHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getBrand>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetBrandSuspenseQueryOptions(brandId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Getting brand document
 * @summary Get Document
 */
export const getDocument = (
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	signal?: AbortSignal
) => {
	return getAxios<BaseSimpleTextModel>({
		url: `/store/brands/${brandId}/document`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetDocumentQueryKey = (
	brandId: number | undefined | null,
	params: GetDocumentParams
) => {
	return [`/store/brands/${brandId}/document`, ...(params ? [params] : [])] as const;
};

export const getGetDocumentQueryOptions = <
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetDocumentQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocument>>> = ({ signal }) =>
		getDocument(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getDocument>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDocumentQueryResult = NonNullable<Awaited<ReturnType<typeof getDocument>>>;
export type GetDocumentQueryError = ErrorType<HTTPValidationError>;

export function useGetDocument<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers: undefined | GetDocumentHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getDocument>>,
					TError,
					Awaited<ReturnType<typeof getDocument>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetDocument<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getDocument>>,
					TError,
					Awaited<ReturnType<typeof getDocument>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetDocument<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Document
 */

export function useGetDocument<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetDocumentQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetDocumentSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetDocumentQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocument>>> = ({ signal }) =>
		getDocument(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getDocument>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDocumentSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getDocument>>>;
export type GetDocumentSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetDocumentSuspense<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers: undefined | GetDocumentHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetDocumentSuspense<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetDocumentSuspense<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Document
 */

export function useGetDocumentSuspense<
	TData = Awaited<ReturnType<typeof getDocument>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetDocumentParams,
	headers?: GetDocumentHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetDocumentSuspenseQueryOptions(brandId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Tree Categories
 */
export const getTreeCategories = (
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CategorySchema[]>({
		url: `/store/categories/tree`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetTreeCategoriesQueryKey = (params: GetTreeCategoriesParams) => {
	return [`/store/categories/tree`, ...(params ? [params] : [])] as const;
};

export const getGetTreeCategoriesQueryOptions = <
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTreeCategoriesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTreeCategories>>> = ({ signal }) =>
		getTreeCategories(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getTreeCategories>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetTreeCategoriesQueryResult = NonNullable<
	Awaited<ReturnType<typeof getTreeCategories>>
>;
export type GetTreeCategoriesQueryError = ErrorType<HTTPValidationError>;

export function useGetTreeCategories<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers: undefined | GetTreeCategoriesHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getTreeCategories>>,
					TError,
					Awaited<ReturnType<typeof getTreeCategories>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTreeCategories<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getTreeCategories>>,
					TError,
					Awaited<ReturnType<typeof getTreeCategories>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTreeCategories<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Tree Categories
 */

export function useGetTreeCategories<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetTreeCategoriesQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetTreeCategoriesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTreeCategoriesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTreeCategories>>> = ({ signal }) =>
		getTreeCategories(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getTreeCategories>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetTreeCategoriesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getTreeCategories>>
>;
export type GetTreeCategoriesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetTreeCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers: undefined | GetTreeCategoriesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTreeCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTreeCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Tree Categories
 */

export function useGetTreeCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getTreeCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetTreeCategoriesParams,
	headers?: GetTreeCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTreeCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetTreeCategoriesSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Categories
 */
export const getCategories = (
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CategorySchema[]>({
		url: `/store/categories/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetCategoriesQueryKey = (params: GetCategoriesParams) => {
	return [`/store/categories/`, ...(params ? [params] : [])] as const;
};

export const getGetCategoriesQueryOptions = <
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCategoriesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCategories>>> = ({ signal }) =>
		getCategories(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getCategories>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCategoriesQueryResult = NonNullable<Awaited<ReturnType<typeof getCategories>>>;
export type GetCategoriesQueryError = ErrorType<HTTPValidationError>;

export function useGetCategories<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers: undefined | GetCategoriesHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCategories>>,
					TError,
					Awaited<ReturnType<typeof getCategories>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCategories<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCategories>>,
					TError,
					Awaited<ReturnType<typeof getCategories>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCategories<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Categories
 */

export function useGetCategories<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCategoriesQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCategoriesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCategoriesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCategories>>> = ({ signal }) =>
		getCategories(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCategories>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCategoriesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCategories>>
>;
export type GetCategoriesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers: undefined | GetCategoriesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Categories
 */

export function useGetCategoriesSuspense<
	TData = Awaited<ReturnType<typeof getCategories>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCategoriesParams,
	headers?: GetCategoriesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCategories>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCategoriesSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns store cities
 * @summary Get Cities
 */
export const getCities = (
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CitiesResponse>({
		url: `/store/cities/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetCitiesQueryKey = (params?: GetCitiesParams) => {
	return [`/store/cities/`, ...(params ? [params] : [])] as const;
};

export const getGetCitiesQueryOptions = <
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCitiesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCities>>> = ({ signal }) =>
		getCities(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getCities>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCitiesQueryResult = NonNullable<Awaited<ReturnType<typeof getCities>>>;
export type GetCitiesQueryError = ErrorType<HTTPValidationError>;

export function useGetCities<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCitiesParams,
	headers: undefined | GetCitiesHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCities>>,
					TError,
					Awaited<ReturnType<typeof getCities>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCities<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCities>>,
					TError,
					Awaited<ReturnType<typeof getCities>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCities<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Cities
 */

export function useGetCities<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCitiesQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCitiesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCitiesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCities>>> = ({ signal }) =>
		getCities(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCities>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCitiesSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getCities>>>;
export type GetCitiesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCitiesSuspense<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCitiesParams,
	headers: undefined | GetCitiesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCitiesSuspense<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCitiesSuspense<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Cities
 */

export function useGetCitiesSuspense<
	TData = Awaited<ReturnType<typeof getCities>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCitiesParams,
	headers?: GetCitiesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCities>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCitiesSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Filter Values
 */
export const getFilterValues = (
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CharacteristicValues>({
		url: `/store/filters/${storeId}/${characteristicId}/values`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetFilterValuesQueryKey = (
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams
) => {
	return [
		`/store/filters/${storeId}/${characteristicId}/values`,
		...(params ? [params] : []),
	] as const;
};

export const getGetFilterValuesQueryOptions = <
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey =
		queryOptions?.queryKey ?? getGetFilterValuesQueryKey(storeId, characteristicId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFilterValues>>> = ({ signal }) =>
		getFilterValues(storeId, characteristicId, params, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(storeId && characteristicId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetFilterValuesQueryResult = NonNullable<Awaited<ReturnType<typeof getFilterValues>>>;
export type GetFilterValuesQueryError = ErrorType<HTTPValidationError>;

export function useGetFilterValues<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params: undefined | GetFilterValuesParams,
	headers: undefined | GetFilterValuesHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFilterValues>>,
					TError,
					Awaited<ReturnType<typeof getFilterValues>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFilterValues<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFilterValues>>,
					TError,
					Awaited<ReturnType<typeof getFilterValues>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFilterValues<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Filter Values
 */

export function useGetFilterValues<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFilterValuesQueryOptions(
		storeId,
		characteristicId,
		params,
		headers,
		options
	);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetFilterValuesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey =
		queryOptions?.queryKey ?? getGetFilterValuesQueryKey(storeId, characteristicId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFilterValues>>> = ({ signal }) =>
		getFilterValues(storeId, characteristicId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getFilterValues>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFilterValuesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getFilterValues>>
>;
export type GetFilterValuesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetFilterValuesSuspense<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params: undefined | GetFilterValuesParams,
	headers: undefined | GetFilterValuesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFilterValuesSuspense<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFilterValuesSuspense<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Filter Values
 */

export function useGetFilterValuesSuspense<
	TData = Awaited<ReturnType<typeof getFilterValues>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	characteristicId: number | undefined | null,
	params?: GetFilterValuesParams,
	headers?: GetFilterValuesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFilterValues>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFilterValuesSuspenseQueryOptions(
		storeId,
		characteristicId,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Create Filters
 */
export const createFilters = (
	createFiltersData: CreateFiltersData,
	headers?: CreateFiltersHeaders,
	signal?: AbortSignal
) => {
	return getAxios<FiltersCreated>({
		url: `/store/filters/create`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: createFiltersData,
		signal,
	});
};

export const getCreateFiltersMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createFilters>>,
		TError,
		{ data: CreateFiltersData; headers?: CreateFiltersHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createFilters>>,
	TError,
	{ data: CreateFiltersData; headers?: CreateFiltersHeaders },
	TContext
> => {
	const mutationKey = ["createFilters"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createFilters>>,
		{ data: CreateFiltersData; headers?: CreateFiltersHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return createFilters(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateFiltersMutationResult = NonNullable<Awaited<ReturnType<typeof createFilters>>>;
export type CreateFiltersMutationBody = CreateFiltersData;
export type CreateFiltersMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create Filters
 */
export const useCreateFilters = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createFilters>>,
		TError,
		{ data: CreateFiltersData; headers?: CreateFiltersHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createFilters>>,
	TError,
	{ data: CreateFiltersData; headers?: CreateFiltersHeaders },
	TContext
> => {
	const mutationOptions = getCreateFiltersMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get Store Filters
 */
export const getStoreFilters = (
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CharacteristicSchema[]>({
		url: `/store/filters/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetStoreFiltersQueryKey = (params?: GetStoreFiltersParams) => {
	return [`/store/filters/`, ...(params ? [params] : [])] as const;
};

export const getGetStoreFiltersQueryOptions = <
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreFiltersQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStoreFilters>>> = ({ signal }) =>
		getStoreFilters(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getStoreFilters>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreFiltersQueryResult = NonNullable<Awaited<ReturnType<typeof getStoreFilters>>>;
export type GetStoreFiltersQueryError = ErrorType<HTTPValidationError>;

export function useGetStoreFilters<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoreFiltersParams,
	headers: undefined | GetStoreFiltersHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStoreFilters>>,
					TError,
					Awaited<ReturnType<typeof getStoreFilters>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreFilters<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStoreFilters>>,
					TError,
					Awaited<ReturnType<typeof getStoreFilters>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreFilters<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store Filters
 */

export function useGetStoreFilters<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreFiltersQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetStoreFiltersSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreFiltersQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStoreFilters>>> = ({ signal }) =>
		getStoreFilters(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getStoreFilters>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreFiltersSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getStoreFilters>>
>;
export type GetStoreFiltersSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetStoreFiltersSuspense<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoreFiltersParams,
	headers: undefined | GetStoreFiltersHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreFiltersSuspense<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreFiltersSuspense<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store Filters
 */

export function useGetStoreFiltersSuspense<
	TData = Awaited<ReturnType<typeof getStoreFilters>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreFiltersParams,
	headers?: GetStoreFiltersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStoreFilters>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreFiltersSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns store available payments
 * @summary Get Available Payments
 */
export const getAvailablePayments = (
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<AvailablePaymentSchema>({
		url: `/store/payments/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetAvailablePaymentsQueryKey = (params?: GetAvailablePaymentsParams) => {
	return [`/store/payments/`, ...(params ? [params] : [])] as const;
};

export const getGetAvailablePaymentsQueryOptions = <
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAvailablePaymentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAvailablePayments>>> = ({ signal }) =>
		getAvailablePayments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getAvailablePayments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAvailablePaymentsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAvailablePayments>>
>;
export type GetAvailablePaymentsQueryError = ErrorType<HTTPValidationError>;

export function useGetAvailablePayments<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetAvailablePaymentsParams,
	headers: undefined | GetAvailablePaymentsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getAvailablePayments>>,
					TError,
					Awaited<ReturnType<typeof getAvailablePayments>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailablePayments<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getAvailablePayments>>,
					TError,
					Awaited<ReturnType<typeof getAvailablePayments>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailablePayments<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Available Payments
 */

export function useGetAvailablePayments<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetAvailablePaymentsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetAvailablePaymentsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAvailablePaymentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAvailablePayments>>> = ({ signal }) =>
		getAvailablePayments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getAvailablePayments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAvailablePaymentsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAvailablePayments>>
>;
export type GetAvailablePaymentsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetAvailablePaymentsSuspense<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetAvailablePaymentsParams,
	headers: undefined | GetAvailablePaymentsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailablePaymentsSuspense<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailablePaymentsSuspense<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Available Payments
 */

export function useGetAvailablePaymentsSuspense<
	TData = Awaited<ReturnType<typeof getAvailablePayments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailablePaymentsParams,
	headers?: GetAvailablePaymentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getAvailablePayments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetAvailablePaymentsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method for getting a list of all incust pay configurations and accounts
 * @summary Get Incust Pay Data
 */
export const getIncustPayData = (
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustPayPaymentData>({
		url: `/store/payments/incust_pay_data`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustPayDataQueryKey = (params?: GetIncustPayDataParams) => {
	return [`/store/payments/incust_pay_data`, ...(params ? [params] : [])] as const;
};

export const getGetIncustPayDataQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustPayDataQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustPayData>>> = ({ signal }) =>
		getIncustPayData(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustPayData>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustPayDataQueryResult = NonNullable<Awaited<ReturnType<typeof getIncustPayData>>>;
export type GetIncustPayDataQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustPayData<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetIncustPayDataParams,
	headers: undefined | GetIncustPayDataHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustPayData>>,
					TError,
					Awaited<ReturnType<typeof getIncustPayData>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustPayData<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustPayData>>,
					TError,
					Awaited<ReturnType<typeof getIncustPayData>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustPayData<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Pay Data
 */

export function useGetIncustPayData<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustPayDataQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustPayDataSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustPayDataQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustPayData>>> = ({ signal }) =>
		getIncustPayData(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustPayData>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustPayDataSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustPayData>>
>;
export type GetIncustPayDataSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustPayDataSuspense<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetIncustPayDataParams,
	headers: undefined | GetIncustPayDataHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustPayDataSuspense<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustPayDataSuspense<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Pay Data
 */

export function useGetIncustPayDataSuspense<
	TData = Awaited<ReturnType<typeof getIncustPayData>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustPayDataParams,
	headers?: GetIncustPayDataHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustPayData>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustPayDataSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns store available payments fee
 * @summary Get Calculated Extra Fee
 */
export const getCalculatedExtraFee = (
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ExtraFeeSchema[]>({
		url: `/store/payments/extra_fee`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetCalculatedExtraFeeQueryKey = (params: GetCalculatedExtraFeeParams) => {
	return [`/store/payments/extra_fee`, ...(params ? [params] : [])] as const;
};

export const getGetCalculatedExtraFeeQueryOptions = <
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCalculatedExtraFee>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCalculatedExtraFeeQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCalculatedExtraFee>>> = ({
		signal,
	}) => getCalculatedExtraFee(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getCalculatedExtraFee>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCalculatedExtraFeeQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCalculatedExtraFee>>
>;
export type GetCalculatedExtraFeeQueryError = ErrorType<HTTPValidationError>;

export function useGetCalculatedExtraFee<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers: undefined | GetCalculatedExtraFeeHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCalculatedExtraFee>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCalculatedExtraFee>>,
					TError,
					Awaited<ReturnType<typeof getCalculatedExtraFee>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCalculatedExtraFee<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCalculatedExtraFee>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCalculatedExtraFee>>,
					TError,
					Awaited<ReturnType<typeof getCalculatedExtraFee>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCalculatedExtraFee<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCalculatedExtraFee>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Calculated Extra Fee
 */

export function useGetCalculatedExtraFee<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCalculatedExtraFee>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCalculatedExtraFeeQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCalculatedExtraFeeSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getCalculatedExtraFee>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCalculatedExtraFeeQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCalculatedExtraFee>>> = ({
		signal,
	}) => getCalculatedExtraFee(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCalculatedExtraFee>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCalculatedExtraFeeSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCalculatedExtraFee>>
>;
export type GetCalculatedExtraFeeSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCalculatedExtraFeeSuspense<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers: undefined | GetCalculatedExtraFeeHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getCalculatedExtraFee>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCalculatedExtraFeeSuspense<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getCalculatedExtraFee>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCalculatedExtraFeeSuspense<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getCalculatedExtraFee>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Calculated Extra Fee
 */

export function useGetCalculatedExtraFeeSuspense<
	TData = Awaited<ReturnType<typeof getCalculatedExtraFee>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetCalculatedExtraFeeParams,
	headers?: GetCalculatedExtraFeeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getCalculatedExtraFee>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCalculatedExtraFeeSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get payment status
 * @summary Get Payment Status
 */
export const getPaymentStatus = (
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	signal?: AbortSignal
) => {
	return getAxios<PaymentStatusSchema>({
		url: `/store/payments/payment_status`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetPaymentStatusQueryKey = (params?: GetPaymentStatusParams) => {
	return [`/store/payments/payment_status`, ...(params ? [params] : [])] as const;
};

export const getGetPaymentStatusQueryOptions = <
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetPaymentStatusQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getPaymentStatus>>> = ({ signal }) =>
		getPaymentStatus(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getPaymentStatus>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetPaymentStatusQueryResult = NonNullable<Awaited<ReturnType<typeof getPaymentStatus>>>;
export type GetPaymentStatusQueryError = ErrorType<HTTPValidationError>;

export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetPaymentStatusParams,
	headers: undefined | GetPaymentStatusHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getPaymentStatus>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getPaymentStatus>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Payment Status
 */

export function useGetPaymentStatus<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetPaymentStatusQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetPaymentStatusSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetPaymentStatusQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getPaymentStatus>>> = ({ signal }) =>
		getPaymentStatus(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getPaymentStatus>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetPaymentStatusSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getPaymentStatus>>
>;
export type GetPaymentStatusSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetPaymentStatusParams,
	headers: undefined | GetPaymentStatusHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Payment Status
 */

export function useGetPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetPaymentStatusParams,
	headers?: GetPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getPaymentStatus>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetPaymentStatusSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Products
 */
export const getProducts = (
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ProductsListResponse>({
		url: `/store/products/${storeId}/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetProductsQueryKey = (
	storeId: number | undefined | null,
	params: GetProductsParams
) => {
	return [`/store/products/${storeId}/`, ...(params ? [params] : [])] as const;
};

export const getGetProductsInfiniteQueryOptions = <
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductsQueryKey(storeId, params);

	const queryFn: QueryFunction<
		Awaited<ReturnType<typeof getProducts>>,
		QueryKey,
		GetProductsParams["cursor"]
	> = ({ signal, pageParam }) =>
		getProducts(
			storeId,
			{ ...params, cursor: pageParam || params?.["cursor"] },
			headers,
			signal
		);

	return { queryKey, queryFn, enabled: !!storeId, ...queryOptions } as UseInfiniteQueryOptions<
		Awaited<ReturnType<typeof getProducts>>,
		TError,
		TData,
		Awaited<ReturnType<typeof getProducts>>,
		QueryKey,
		GetProductsParams["cursor"]
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProductsInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof getProducts>>>;
export type GetProductsInfiniteQueryError = ErrorType<HTTPValidationError>;

export function useGetProductsInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers: undefined | GetProductsHeaders,
	options: {
		query: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProducts>>,
					TError,
					Awaited<ReturnType<typeof getProducts>>,
					QueryKey
				>,
				"initialData"
			>;
	}
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProducts>>,
					TError,
					Awaited<ReturnType<typeof getProducts>>,
					QueryKey
				>,
				"initialData"
			>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Products
 */

export function useGetProductsInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductsInfiniteQueryOptions(storeId, params, headers, options);

	const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetProductsQueryOptions = <
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductsQueryKey(storeId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getProducts>>> = ({ signal }) =>
		getProducts(storeId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!storeId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getProducts>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProductsQueryResult = NonNullable<Awaited<ReturnType<typeof getProducts>>>;
export type GetProductsQueryError = ErrorType<HTTPValidationError>;

export function useGetProducts<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers: undefined | GetProductsHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProducts>>,
					TError,
					Awaited<ReturnType<typeof getProducts>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProducts<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProducts>>,
					TError,
					Awaited<ReturnType<typeof getProducts>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProducts<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Products
 */

export function useGetProducts<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductsQueryOptions(storeId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetProductsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductsQueryKey(storeId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getProducts>>> = ({ signal }) =>
		getProducts(storeId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getProducts>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProductsSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getProducts>>>;
export type GetProductsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetProductsSuspense<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers: undefined | GetProductsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsSuspense<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsSuspense<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Products
 */

export function useGetProductsSuspense<
	TData = Awaited<ReturnType<typeof getProducts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProducts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductsSuspenseQueryOptions(storeId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetProductsSuspenseInfiniteQueryOptions = <
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductsQueryKey(storeId, params);

	const queryFn: QueryFunction<
		Awaited<ReturnType<typeof getProducts>>,
		QueryKey,
		GetProductsParams["cursor"]
	> = ({ signal, pageParam }) =>
		getProducts(
			storeId,
			{ ...params, cursor: pageParam || params?.["cursor"] },
			headers,
			signal
		);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseInfiniteQueryOptions<
		Awaited<ReturnType<typeof getProducts>>,
		TError,
		TData,
		Awaited<ReturnType<typeof getProducts>>,
		QueryKey,
		GetProductsParams["cursor"]
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProductsSuspenseInfiniteQueryResult = NonNullable<
	Awaited<ReturnType<typeof getProducts>>
>;
export type GetProductsSuspenseInfiniteQueryError = ErrorType<HTTPValidationError>;

export function useGetProductsSuspenseInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers: undefined | GetProductsHeaders,
	options: {
		query: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsSuspenseInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductsSuspenseInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Products
 */

export function useGetProductsSuspenseInfinite<
	TData = InfiniteData<Awaited<ReturnType<typeof getProducts>>, GetProductsParams["cursor"]>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: GetProductsParams,
	headers?: GetProductsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof getProducts>>,
				TError,
				TData,
				Awaited<ReturnType<typeof getProducts>>,
				QueryKey,
				GetProductsParams["cursor"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductsSuspenseInfiniteQueryOptions(
		storeId,
		params,
		headers,
		options
	);

	const query = useSuspenseInfiniteQuery(queryOptions) as UseSuspenseInfiniteQueryResult<
		TData,
		TError
	> & { queryKey: DataTag<QueryKey, TData, TError> };

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Min Max Prices
 */
export const getMinMaxPrices = (
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ProductsMinMaxPrices>({
		url: `/store/products/${storeId}/minMaxPrices`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetMinMaxPricesQueryKey = (
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams
) => {
	return [`/store/products/${storeId}/minMaxPrices`, ...(params ? [params] : [])] as const;
};

export const getGetMinMaxPricesQueryOptions = <
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMinMaxPricesQueryKey(storeId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMinMaxPrices>>> = ({ signal }) =>
		getMinMaxPrices(storeId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!storeId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getMinMaxPrices>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMinMaxPricesQueryResult = NonNullable<Awaited<ReturnType<typeof getMinMaxPrices>>>;
export type GetMinMaxPricesQueryError = ErrorType<HTTPValidationError>;

export function useGetMinMaxPrices<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: undefined | GetMinMaxPricesParams,
	headers: undefined | GetMinMaxPricesHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMinMaxPrices>>,
					TError,
					Awaited<ReturnType<typeof getMinMaxPrices>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMinMaxPrices<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMinMaxPrices>>,
					TError,
					Awaited<ReturnType<typeof getMinMaxPrices>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMinMaxPrices<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Min Max Prices
 */

export function useGetMinMaxPrices<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMinMaxPricesQueryOptions(storeId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetMinMaxPricesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMinMaxPricesQueryKey(storeId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMinMaxPrices>>> = ({ signal }) =>
		getMinMaxPrices(storeId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getMinMaxPrices>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMinMaxPricesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getMinMaxPrices>>
>;
export type GetMinMaxPricesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetMinMaxPricesSuspense<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params: undefined | GetMinMaxPricesParams,
	headers: undefined | GetMinMaxPricesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMinMaxPricesSuspense<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMinMaxPricesSuspense<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Min Max Prices
 */

export function useGetMinMaxPricesSuspense<
	TData = Awaited<ReturnType<typeof getMinMaxPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	params?: GetMinMaxPricesParams,
	headers?: GetMinMaxPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMinMaxPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMinMaxPricesSuspenseQueryOptions(storeId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Product
 */
export const getProduct = (
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ProductSchema>({
		url: `/store/products/${storeId}/${productId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetProductQueryKey = (
	storeId: number | undefined | null,
	productId: number | undefined | null
) => {
	return [`/store/products/${storeId}/${productId}`] as const;
};

export const getGetProductQueryOptions = <
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductQueryKey(storeId, productId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getProduct>>> = ({ signal }) =>
		getProduct(storeId, productId, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(storeId && productId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetProductQueryResult = NonNullable<Awaited<ReturnType<typeof getProduct>>>;
export type GetProductQueryError = ErrorType<HTTPValidationError>;

export function useGetProduct<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers: undefined | GetProductHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProduct>>,
					TError,
					Awaited<ReturnType<typeof getProduct>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProduct<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getProduct>>,
					TError,
					Awaited<ReturnType<typeof getProduct>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProduct<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Product
 */

export function useGetProduct<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductQueryOptions(storeId, productId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetProductSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetProductQueryKey(storeId, productId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getProduct>>> = ({ signal }) =>
		getProduct(storeId, productId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getProduct>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProductSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getProduct>>>;
export type GetProductSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetProductSuspense<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers: undefined | GetProductHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductSuspense<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetProductSuspense<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Product
 */

export function useGetProductSuspense<
	TData = Awaited<ReturnType<typeof getProduct>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: GetProductHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getProduct>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetProductSuspenseQueryOptions(storeId, productId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Product Modification
 */
export const getProductModification = (
	storeId: number | undefined | null,
	productId: number | undefined | null,
	findProductModificationData: FindProductModificationData,
	headers?: GetProductModificationHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ProductSchema>({
		url: `/store/products/${storeId}/${productId}/findModification`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: findProductModificationData,
		signal,
	});
};

export const getGetProductModificationMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getProductModification>>,
		TError,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			data: FindProductModificationData;
			headers?: GetProductModificationHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof getProductModification>>,
	TError,
	{
		storeId: number | undefined | null;
		productId: number | undefined | null;
		data: FindProductModificationData;
		headers?: GetProductModificationHeaders;
	},
	TContext
> => {
	const mutationKey = ["getProductModification"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof getProductModification>>,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			data: FindProductModificationData;
			headers?: GetProductModificationHeaders;
		}
	> = props => {
		const { storeId, productId, data, headers } = props ?? {};

		return getProductModification(storeId, productId, data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type GetProductModificationMutationResult = NonNullable<
	Awaited<ReturnType<typeof getProductModification>>
>;
export type GetProductModificationMutationBody = FindProductModificationData;
export type GetProductModificationMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Get Product Modification
 */
export const useGetProductModification = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof getProductModification>>,
		TError,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			data: FindProductModificationData;
			headers?: GetProductModificationHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof getProductModification>>,
	TError,
	{
		storeId: number | undefined | null;
		productId: number | undefined | null;
		data: FindProductModificationData;
		headers?: GetProductModificationHeaders;
	},
	TContext
> => {
	const mutationOptions = getGetProductModificationMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * @summary Get Shipments
 */
export const getShipments = (
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ShipmentsData>({
		url: `/store/shipments/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetShipmentsQueryKey = (params?: GetShipmentsParams) => {
	return [`/store/shipments/`, ...(params ? [params] : [])] as const;
};

export const getGetShipmentsQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipments>>> = ({ signal }) =>
		getShipments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getShipments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentsQueryResult = NonNullable<Awaited<ReturnType<typeof getShipments>>>;
export type GetShipmentsQueryError = ErrorType<HTTPValidationError>;

export function useGetShipments<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentsParams,
	headers: undefined | GetShipmentsHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipments>>,
					TError,
					Awaited<ReturnType<typeof getShipments>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipments<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipments>>,
					TError,
					Awaited<ReturnType<typeof getShipments>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipments<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipments
 */

export function useGetShipments<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetShipmentsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipments>>> = ({ signal }) =>
		getShipments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getShipments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentsSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getShipments>>>;
export type GetShipmentsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentsParams,
	headers: undefined | GetShipmentsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipments
 */

export function useGetShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentsParams,
	headers?: GetShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method for getting a list of all custom shipment methods of the store
 * @summary Get Custom Shipments
 */
export const getCustomShipments = (
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CustomShipmentsSchema>({
		url: `/store/shipments/custom_shipments`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetCustomShipmentsQueryKey = (params?: GetCustomShipmentsParams) => {
	return [`/store/shipments/custom_shipments`, ...(params ? [params] : [])] as const;
};

export const getGetCustomShipmentsQueryOptions = <
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCustomShipmentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCustomShipments>>> = ({ signal }) =>
		getCustomShipments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getCustomShipments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCustomShipmentsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCustomShipments>>
>;
export type GetCustomShipmentsQueryError = ErrorType<HTTPValidationError>;

export function useGetCustomShipments<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCustomShipmentsParams,
	headers: undefined | GetCustomShipmentsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCustomShipments>>,
					TError,
					Awaited<ReturnType<typeof getCustomShipments>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCustomShipments<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCustomShipments>>,
					TError,
					Awaited<ReturnType<typeof getCustomShipments>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCustomShipments<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Custom Shipments
 */

export function useGetCustomShipments<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCustomShipmentsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCustomShipmentsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCustomShipmentsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCustomShipments>>> = ({ signal }) =>
		getCustomShipments(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCustomShipments>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCustomShipmentsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCustomShipments>>
>;
export type GetCustomShipmentsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCustomShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetCustomShipmentsParams,
	headers: undefined | GetCustomShipmentsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCustomShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCustomShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Custom Shipments
 */

export function useGetCustomShipmentsSuspense<
	TData = Awaited<ReturnType<typeof getCustomShipments>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetCustomShipmentsParams,
	headers?: GetCustomShipmentsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCustomShipments>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCustomShipmentsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method for getting a list of all shipment prices of the order
 * @summary Get Shipment Prices
 */
export const getShipmentPrices = (
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ShipmentPriceResultData[]>({
		url: `/store/shipments/prices`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetShipmentPricesQueryKey = (params?: GetShipmentPricesParams) => {
	return [`/store/shipments/prices`, ...(params ? [params] : [])] as const;
};

export const getGetShipmentPricesQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentPricesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipmentPrices>>> = ({ signal }) =>
		getShipmentPrices(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getShipmentPrices>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentPricesQueryResult = NonNullable<
	Awaited<ReturnType<typeof getShipmentPrices>>
>;
export type GetShipmentPricesQueryError = ErrorType<HTTPValidationError>;

export function useGetShipmentPrices<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentPricesParams,
	headers: undefined | GetShipmentPricesHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipmentPrices>>,
					TError,
					Awaited<ReturnType<typeof getShipmentPrices>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPrices<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipmentPrices>>,
					TError,
					Awaited<ReturnType<typeof getShipmentPrices>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPrices<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipment Prices
 */

export function useGetShipmentPrices<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentPricesQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetShipmentPricesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentPricesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipmentPrices>>> = ({ signal }) =>
		getShipmentPrices(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getShipmentPrices>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentPricesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getShipmentPrices>>
>;
export type GetShipmentPricesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetShipmentPricesSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentPricesParams,
	headers: undefined | GetShipmentPricesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPricesSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPricesSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipment Prices
 */

export function useGetShipmentPricesSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrices>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPricesParams,
	headers?: GetShipmentPricesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrices>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentPricesSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method for getting shipment price of the order
 * @summary Get Shipment Price
 */
export const getShipmentPrice = (
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ShipmentPrice>({
		url: `/store/shipments/price`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetShipmentPriceQueryKey = (params?: GetShipmentPriceParams) => {
	return [`/store/shipments/price`, ...(params ? [params] : [])] as const;
};

export const getGetShipmentPriceQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentPriceQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipmentPrice>>> = ({ signal }) =>
		getShipmentPrice(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getShipmentPrice>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentPriceQueryResult = NonNullable<Awaited<ReturnType<typeof getShipmentPrice>>>;
export type GetShipmentPriceQueryError = ErrorType<HTTPValidationError>;

export function useGetShipmentPrice<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentPriceParams,
	headers: undefined | GetShipmentPriceHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipmentPrice>>,
					TError,
					Awaited<ReturnType<typeof getShipmentPrice>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPrice<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getShipmentPrice>>,
					TError,
					Awaited<ReturnType<typeof getShipmentPrice>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPrice<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipment Price
 */

export function useGetShipmentPrice<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentPriceQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetShipmentPriceSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetShipmentPriceQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipmentPrice>>> = ({ signal }) =>
		getShipmentPrice(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getShipmentPrice>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetShipmentPriceSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getShipmentPrice>>
>;
export type GetShipmentPriceSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetShipmentPriceSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetShipmentPriceParams,
	headers: undefined | GetShipmentPriceHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPriceSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetShipmentPriceSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Shipment Price
 */

export function useGetShipmentPriceSuspense<
	TData = Awaited<ReturnType<typeof getShipmentPrice>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetShipmentPriceParams,
	headers?: GetShipmentPriceHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getShipmentPrice>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetShipmentPriceSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method get stores
 * @summary Get Stores
 */
export const getStores = (
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GetStores200>({
		url: `/store/stores/`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetStoresQueryKey = (params?: GetStoresParams) => {
	return [`/store/stores/`, ...(params ? [params] : [])] as const;
};

export const getGetStoresQueryOptions = <
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoresQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStores>>> = ({ signal }) =>
		getStores(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getStores>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoresQueryResult = NonNullable<Awaited<ReturnType<typeof getStores>>>;
export type GetStoresQueryError = ErrorType<HTTPValidationError>;

export function useGetStores<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoresParams,
	headers: undefined | GetStoresHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStores>>,
					TError,
					Awaited<ReturnType<typeof getStores>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStores<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStores>>,
					TError,
					Awaited<ReturnType<typeof getStores>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStores<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Stores
 */

export function useGetStores<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoresQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetStoresSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoresQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStores>>> = ({ signal }) =>
		getStores(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getStores>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoresSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getStores>>>;
export type GetStoresSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetStoresSuspense<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoresParams,
	headers: undefined | GetStoresHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoresSuspense<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoresSuspense<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Stores
 */

export function useGetStoresSuspense<
	TData = Awaited<ReturnType<typeof getStores>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoresParams,
	headers?: GetStoresHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStores>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoresSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns store
 * @summary Get Store
 */
export const getStore = (
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	signal?: AbortSignal
) => {
	return getAxios<StoreSchema>({
		url: `/store/stores/${storeId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetStoreQueryKey = (storeId: number | undefined | null) => {
	return [`/store/stores/${storeId}`] as const;
};

export const getGetStoreQueryOptions = <
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreQueryKey(storeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStore>>> = ({ signal }) =>
		getStore(storeId, headers, signal);

	return { queryKey, queryFn, enabled: !!storeId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getStore>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreQueryResult = NonNullable<Awaited<ReturnType<typeof getStore>>>;
export type GetStoreQueryError = ErrorType<HTTPValidationError>;

export function useGetStore<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers: undefined | GetStoreHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStore>>,
					TError,
					Awaited<ReturnType<typeof getStore>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStore<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStore>>,
					TError,
					Awaited<ReturnType<typeof getStore>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStore<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store
 */

export function useGetStore<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreQueryOptions(storeId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetStoreSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreQueryKey(storeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStore>>> = ({ signal }) =>
		getStore(storeId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getStore>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getStore>>>;
export type GetStoreSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetStoreSuspense<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers: undefined | GetStoreHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreSuspense<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreSuspense<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store
 */

export function useGetStoreSuspense<
	TData = Awaited<ReturnType<typeof getStore>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetStoreHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getStore>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreSuspenseQueryOptions(storeId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Returns stores list
 * @summary Get Store By Coordinates Or Address
 */
export const getStoreByCoordinatesOrAddress = (
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	signal?: AbortSignal
) => {
	return getAxios<StoreSchema[]>({
		url: `/store/stores/by_coordinates_or_address`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetStoreByCoordinatesOrAddressQueryKey = (
	params?: GetStoreByCoordinatesOrAddressParams
) => {
	return [`/store/stores/by_coordinates_or_address`, ...(params ? [params] : [])] as const;
};

export const getGetStoreByCoordinatesOrAddressQueryOptions = <
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreByCoordinatesOrAddressQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>> = ({
		signal,
	}) => getStoreByCoordinatesOrAddress(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreByCoordinatesOrAddressQueryResult = NonNullable<
	Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>
>;
export type GetStoreByCoordinatesOrAddressQueryError = ErrorType<HTTPValidationError>;

export function useGetStoreByCoordinatesOrAddress<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoreByCoordinatesOrAddressParams,
	headers: undefined | GetStoreByCoordinatesOrAddressHeaders,
	options: {
		query: Partial<
			UseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
					TError,
					Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreByCoordinatesOrAddress<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
					TError,
					Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreByCoordinatesOrAddress<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store By Coordinates Or Address
 */

export function useGetStoreByCoordinatesOrAddress<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreByCoordinatesOrAddressQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetStoreByCoordinatesOrAddressSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetStoreByCoordinatesOrAddressQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>> = ({
		signal,
	}) => getStoreByCoordinatesOrAddress(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetStoreByCoordinatesOrAddressSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>
>;
export type GetStoreByCoordinatesOrAddressSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetStoreByCoordinatesOrAddressSuspense<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetStoreByCoordinatesOrAddressParams,
	headers: undefined | GetStoreByCoordinatesOrAddressHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreByCoordinatesOrAddressSuspense<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetStoreByCoordinatesOrAddressSuspense<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Store By Coordinates Or Address
 */

export function useGetStoreByCoordinatesOrAddressSuspense<
	TData = Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetStoreByCoordinatesOrAddressParams,
	headers?: GetStoreByCoordinatesOrAddressHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getStoreByCoordinatesOrAddress>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetStoreByCoordinatesOrAddressSuspenseQueryOptions(
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to send text notification
 * @summary Send Text Notification
 */
export const sendTextNotification = (
	sendTextNotificationData: SendTextNotificationData,
	headers?: SendTextNotificationHeaders,
	signal?: AbortSignal
) => {
	return getAxios<unknown>({
		url: `/store/text_notifications/send`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: sendTextNotificationData,
		signal,
	});
};

export const getSendTextNotificationMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendTextNotification>>,
		TError,
		{ data: SendTextNotificationData; headers?: SendTextNotificationHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof sendTextNotification>>,
	TError,
	{ data: SendTextNotificationData; headers?: SendTextNotificationHeaders },
	TContext
> => {
	const mutationKey = ["sendTextNotification"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof sendTextNotification>>,
		{ data: SendTextNotificationData; headers?: SendTextNotificationHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return sendTextNotification(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type SendTextNotificationMutationResult = NonNullable<
	Awaited<ReturnType<typeof sendTextNotification>>
>;
export type SendTextNotificationMutationBody = SendTextNotificationData;
export type SendTextNotificationMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Send Text Notification
 */
export const useSendTextNotification = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendTextNotification>>,
		TError,
		{ data: SendTextNotificationData; headers?: SendTextNotificationHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof sendTextNotification>>,
	TError,
	{ data: SendTextNotificationData; headers?: SendTextNotificationHeaders },
	TContext
> => {
	const mutationOptions = getSendTextNotificationMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to cancel order
 * @summary Cancel Store Order
 */
export const cancelStoreOrder = (
	orderID: OrderID,
	headers?: CancelStoreOrderHeaders,
	signal?: AbortSignal
) => {
	return getAxios<unknown>({
		url: `/store/order/cancel_order`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: orderID,
		signal,
	});
};

export const getCancelStoreOrderMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof cancelStoreOrder>>,
		TError,
		{ data: OrderID; headers?: CancelStoreOrderHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof cancelStoreOrder>>,
	TError,
	{ data: OrderID; headers?: CancelStoreOrderHeaders },
	TContext
> => {
	const mutationKey = ["cancelStoreOrder"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof cancelStoreOrder>>,
		{ data: OrderID; headers?: CancelStoreOrderHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return cancelStoreOrder(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type CancelStoreOrderMutationResult = NonNullable<
	Awaited<ReturnType<typeof cancelStoreOrder>>
>;
export type CancelStoreOrderMutationBody = OrderID;
export type CancelStoreOrderMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Cancel Store Order
 */
export const useCancelStoreOrder = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof cancelStoreOrder>>,
		TError,
		{ data: OrderID; headers?: CancelStoreOrderHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof cancelStoreOrder>>,
	TError,
	{ data: OrderID; headers?: CancelStoreOrderHeaders },
	TContext
> => {
	const mutationOptions = getCancelStoreOrderMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get user orders list
 * @summary Get User Orders
 */
export const getUserOrders = (
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GetUserOrders200>({
		url: `/store/order/get_by_user`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetUserOrdersQueryKey = (params: GetUserOrdersParams) => {
	return [`/store/order/get_by_user`, ...(params ? [params] : [])] as const;
};

export const getGetUserOrdersQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserOrdersQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserOrders>>> = ({ signal }) =>
		getUserOrders(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUserOrders>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserOrdersQueryResult = NonNullable<Awaited<ReturnType<typeof getUserOrders>>>;
export type GetUserOrdersQueryError = ErrorType<HTTPValidationError>;

export function useGetUserOrders<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers: undefined | GetUserOrdersHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserOrders>>,
					TError,
					Awaited<ReturnType<typeof getUserOrders>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserOrders<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserOrders>>,
					TError,
					Awaited<ReturnType<typeof getUserOrders>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserOrders<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Orders
 */

export function useGetUserOrders<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserOrdersQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserOrdersSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserOrdersQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserOrders>>> = ({ signal }) =>
		getUserOrders(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUserOrders>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserOrdersSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserOrders>>
>;
export type GetUserOrdersSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetUserOrdersSuspense<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers: undefined | GetUserOrdersHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserOrdersSuspense<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserOrdersSuspense<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Orders
 */

export function useGetUserOrdersSuspense<
	TData = Awaited<ReturnType<typeof getUserOrders>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetUserOrdersParams,
	headers?: GetUserOrdersHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserOrders>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserOrdersSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get order payment status
 * @summary Get Order Payment Status
 */
export const getOrderPaymentStatus = (
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderPaymentStatusSchema>({
		url: `/store/order/payment_status/${orderId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetOrderPaymentStatusQueryKey = (orderId: number | undefined | null) => {
	return [`/store/order/payment_status/${orderId}`] as const;
};

export const getGetOrderPaymentStatusQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderPaymentStatus>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderPaymentStatusQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrderPaymentStatus>>> = ({
		signal,
	}) => getOrderPaymentStatus(orderId, headers, signal);

	return { queryKey, queryFn, enabled: !!orderId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getOrderPaymentStatus>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderPaymentStatusQueryResult = NonNullable<
	Awaited<ReturnType<typeof getOrderPaymentStatus>>
>;
export type GetOrderPaymentStatusQueryError = ErrorType<HTTPValidationError>;

export function useGetOrderPaymentStatus<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderPaymentStatusHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderPaymentStatus>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrderPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getOrderPaymentStatus>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderPaymentStatus<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderPaymentStatus>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrderPaymentStatus>>,
					TError,
					Awaited<ReturnType<typeof getOrderPaymentStatus>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderPaymentStatus<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order Payment Status
 */

export function useGetOrderPaymentStatus<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderPaymentStatus>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderPaymentStatusQueryOptions(orderId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetOrderPaymentStatusSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderPaymentStatus>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderPaymentStatusQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrderPaymentStatus>>> = ({
		signal,
	}) => getOrderPaymentStatus(orderId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getOrderPaymentStatus>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderPaymentStatusSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getOrderPaymentStatus>>
>;
export type GetOrderPaymentStatusSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetOrderPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderPaymentStatusHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderPaymentStatus>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderPaymentStatus>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderPaymentStatus>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order Payment Status
 */

export function useGetOrderPaymentStatusSuspense<
	TData = Awaited<ReturnType<typeof getOrderPaymentStatus>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderPaymentStatusHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderPaymentStatus>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderPaymentStatusSuspenseQueryOptions(orderId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to send store order invoice to client bot
 * @summary Send Order Invoice To Bot
 */
export const sendOrderInvoiceToBot = (
	orderId: number | undefined | null,
	params?: SendOrderInvoiceToBotParams,
	headers?: SendOrderInvoiceToBotHeaders,
	signal?: AbortSignal
) => {
	return getAxios<unknown>({
		url: `/store/order/send_invoice_to_bot/${orderId}`,
		method: "POST",
		headers,
		params,
		signal,
	});
};

export const getSendOrderInvoiceToBotMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendOrderInvoiceToBot>>,
		TError,
		{
			orderId: number | undefined | null;
			params?: SendOrderInvoiceToBotParams;
			headers?: SendOrderInvoiceToBotHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof sendOrderInvoiceToBot>>,
	TError,
	{
		orderId: number | undefined | null;
		params?: SendOrderInvoiceToBotParams;
		headers?: SendOrderInvoiceToBotHeaders;
	},
	TContext
> => {
	const mutationKey = ["sendOrderInvoiceToBot"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof sendOrderInvoiceToBot>>,
		{
			orderId: number | undefined | null;
			params?: SendOrderInvoiceToBotParams;
			headers?: SendOrderInvoiceToBotHeaders;
		}
	> = props => {
		const { orderId, params, headers } = props ?? {};

		return sendOrderInvoiceToBot(orderId, params, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type SendOrderInvoiceToBotMutationResult = NonNullable<
	Awaited<ReturnType<typeof sendOrderInvoiceToBot>>
>;

export type SendOrderInvoiceToBotMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Send Order Invoice To Bot
 */
export const useSendOrderInvoiceToBot = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendOrderInvoiceToBot>>,
		TError,
		{
			orderId: number | undefined | null;
			params?: SendOrderInvoiceToBotParams;
			headers?: SendOrderInvoiceToBotHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof sendOrderInvoiceToBot>>,
	TError,
	{
		orderId: number | undefined | null;
		params?: SendOrderInvoiceToBotParams;
		headers?: SendOrderInvoiceToBotHeaders;
	},
	TContext
> => {
	const mutationOptions = getSendOrderInvoiceToBotMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get order history statuses
 * @summary Get Order History Statuses
 */
export const getOrderHistoryStatuses = (
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderHistorySchema[]>({
		url: `/store/order/${orderId}/history`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetOrderHistoryStatusesQueryKey = (orderId: number | undefined | null) => {
	return [`/store/order/${orderId}/history`] as const;
};

export const getGetOrderHistoryStatusesQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderHistoryStatuses>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderHistoryStatusesQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrderHistoryStatuses>>> = ({
		signal,
	}) => getOrderHistoryStatuses(orderId, headers, signal);

	return { queryKey, queryFn, enabled: !!orderId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderHistoryStatusesQueryResult = NonNullable<
	Awaited<ReturnType<typeof getOrderHistoryStatuses>>
>;
export type GetOrderHistoryStatusesQueryError = ErrorType<HTTPValidationError>;

export function useGetOrderHistoryStatuses<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderHistoryStatusesHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderHistoryStatuses>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
					TError,
					Awaited<ReturnType<typeof getOrderHistoryStatuses>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderHistoryStatuses<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderHistoryStatuses>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
					TError,
					Awaited<ReturnType<typeof getOrderHistoryStatuses>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderHistoryStatuses<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderHistoryStatuses>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order History Statuses
 */

export function useGetOrderHistoryStatuses<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getOrderHistoryStatuses>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderHistoryStatusesQueryOptions(orderId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetOrderHistoryStatusesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderHistoryStatusesQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrderHistoryStatuses>>> = ({
		signal,
	}) => getOrderHistoryStatuses(orderId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderHistoryStatusesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getOrderHistoryStatuses>>
>;
export type GetOrderHistoryStatusesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetOrderHistoryStatusesSuspense<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderHistoryStatusesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderHistoryStatusesSuspense<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderHistoryStatusesSuspense<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order History Statuses
 */

export function useGetOrderHistoryStatusesSuspense<
	TData = Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHistoryStatusesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getOrderHistoryStatuses>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderHistoryStatusesSuspenseQueryOptions(orderId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get order by order_token or user
 * @summary Get Order
 */
export const getOrder = (
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderSchema>({
		url: `/store/order/${orderId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetOrderQueryKey = (orderId: number | undefined | null) => {
	return [`/store/order/${orderId}`] as const;
};

export const getGetOrderQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrder>>> = ({ signal }) =>
		getOrder(orderId, headers, signal);

	return { queryKey, queryFn, enabled: !!orderId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getOrder>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderQueryResult = NonNullable<Awaited<ReturnType<typeof getOrder>>>;
export type GetOrderQueryError = ErrorType<HTTPValidationError>;

export function useGetOrder<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrder>>,
					TError,
					Awaited<ReturnType<typeof getOrder>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrder<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getOrder>>,
					TError,
					Awaited<ReturnType<typeof getOrder>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrder<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order
 */

export function useGetOrder<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderQueryOptions(orderId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetOrderSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetOrderQueryKey(orderId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getOrder>>> = ({ signal }) =>
		getOrder(orderId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getOrder>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetOrderSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getOrder>>>;
export type GetOrderSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetOrderSuspense<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers: undefined | GetOrderHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderSuspense<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetOrderSuspense<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Order
 */

export function useGetOrderSuspense<
	TData = Awaited<ReturnType<typeof getOrder>>,
	TError = ErrorType<HTTPValidationError>,
>(
	orderId: number | undefined | null,
	headers?: GetOrderHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getOrder>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetOrderSuspenseQueryOptions(orderId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to update order by order_token or user
 * @summary Update Store Order
 */
export const updateStoreOrder = (
	orderId: number | undefined | null,
	updateOrderSchema: UpdateOrderSchema,
	headers?: UpdateStoreOrderHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderSchema>({
		url: `/store/order/update/${orderId}`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: updateOrderSchema,
		signal,
	});
};

export const getUpdateStoreOrderMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateStoreOrder>>,
		TError,
		{
			orderId: number | undefined | null;
			data: UpdateOrderSchema;
			headers?: UpdateStoreOrderHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof updateStoreOrder>>,
	TError,
	{
		orderId: number | undefined | null;
		data: UpdateOrderSchema;
		headers?: UpdateStoreOrderHeaders;
	},
	TContext
> => {
	const mutationKey = ["updateStoreOrder"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof updateStoreOrder>>,
		{
			orderId: number | undefined | null;
			data: UpdateOrderSchema;
			headers?: UpdateStoreOrderHeaders;
		}
	> = props => {
		const { orderId, data, headers } = props ?? {};

		return updateStoreOrder(orderId, data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type UpdateStoreOrderMutationResult = NonNullable<
	Awaited<ReturnType<typeof updateStoreOrder>>
>;
export type UpdateStoreOrderMutationBody = UpdateOrderSchema;
export type UpdateStoreOrderMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Update Store Order
 */
export const useUpdateStoreOrder = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof updateStoreOrder>>,
		TError,
		{
			orderId: number | undefined | null;
			data: UpdateOrderSchema;
			headers?: UpdateStoreOrderHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof updateStoreOrder>>,
	TError,
	{
		orderId: number | undefined | null;
		data: UpdateOrderSchema;
		headers?: UpdateStoreOrderHeaders;
	},
	TContext
> => {
	const mutationOptions = getUpdateStoreOrderMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to create order
 * @summary Create Order
 */
export const createOrder = (
	createOrderSchema: CreateOrderSchema,
	headers?: CreateOrderHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderSchema>({
		url: `/store/order/create`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: createOrderSchema,
		signal,
	});
};

export const getCreateOrderMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createOrder>>,
		TError,
		{ data: CreateOrderSchema; headers?: CreateOrderHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createOrder>>,
	TError,
	{ data: CreateOrderSchema; headers?: CreateOrderHeaders },
	TContext
> => {
	const mutationKey = ["createOrder"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createOrder>>,
		{ data: CreateOrderSchema; headers?: CreateOrderHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return createOrder(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateOrderMutationResult = NonNullable<Awaited<ReturnType<typeof createOrder>>>;
export type CreateOrderMutationBody = CreateOrderSchema;
export type CreateOrderMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create Order
 */
export const useCreateOrder = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createOrder>>,
		TError,
		{ data: CreateOrderSchema; headers?: CreateOrderHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createOrder>>,
	TError,
	{ data: CreateOrderSchema; headers?: CreateOrderHeaders },
	TContext
> => {
	const mutationOptions = getCreateOrderMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to create order in sore
 * @summary Create Order In Store
 */
export const createOrderInStore = (
	createOrderInStoreSchema: CreateOrderInStoreSchema,
	headers?: CreateOrderInStoreHeaders,
	signal?: AbortSignal
) => {
	return getAxios<OrderSchema>({
		url: `/store/order/createInStore`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: createOrderInStoreSchema,
		signal,
	});
};

export const getCreateOrderInStoreMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createOrderInStore>>,
		TError,
		{ data: CreateOrderInStoreSchema; headers?: CreateOrderInStoreHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof createOrderInStore>>,
	TError,
	{ data: CreateOrderInStoreSchema; headers?: CreateOrderInStoreHeaders },
	TContext
> => {
	const mutationKey = ["createOrderInStore"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof createOrderInStore>>,
		{ data: CreateOrderInStoreSchema; headers?: CreateOrderInStoreHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return createOrderInStore(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type CreateOrderInStoreMutationResult = NonNullable<
	Awaited<ReturnType<typeof createOrderInStore>>
>;
export type CreateOrderInStoreMutationBody = CreateOrderInStoreSchema;
export type CreateOrderInStoreMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Create Order In Store
 */
export const useCreateOrderInStore = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof createOrderInStore>>,
		TError,
		{ data: CreateOrderInStoreSchema; headers?: CreateOrderInStoreHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof createOrderInStore>>,
	TError,
	{ data: CreateOrderInStoreSchema; headers?: CreateOrderInStoreHeaders },
	TContext
> => {
	const mutationOptions = getCreateOrderInStoreMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns favorites list
 * @summary Get Favorites
 */
export const getFavorites = (
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	signal?: AbortSignal
) => {
	return getAxios<FavoritesSchema>({
		url: `/store/favorites/${storeId}/`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetFavoritesQueryKey = (storeId: number | undefined | null) => {
	return [`/store/favorites/${storeId}/`] as const;
};

export const getGetFavoritesQueryOptions = <
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFavoritesQueryKey(storeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFavorites>>> = ({ signal }) =>
		getFavorites(storeId, headers, signal);

	return { queryKey, queryFn, enabled: !!storeId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getFavorites>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFavoritesQueryResult = NonNullable<Awaited<ReturnType<typeof getFavorites>>>;
export type GetFavoritesQueryError = ErrorType<HTTPValidationError>;

export function useGetFavorites<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers: undefined | GetFavoritesHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFavorites>>,
					TError,
					Awaited<ReturnType<typeof getFavorites>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFavorites<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getFavorites>>,
					TError,
					Awaited<ReturnType<typeof getFavorites>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFavorites<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Favorites
 */

export function useGetFavorites<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFavoritesQueryOptions(storeId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetFavoritesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetFavoritesQueryKey(storeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getFavorites>>> = ({ signal }) =>
		getFavorites(storeId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getFavorites>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetFavoritesSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getFavorites>>>;
export type GetFavoritesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetFavoritesSuspense<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers: undefined | GetFavoritesHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFavoritesSuspense<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetFavoritesSuspense<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Favorites
 */

export function useGetFavoritesSuspense<
	TData = Awaited<ReturnType<typeof getFavorites>>,
	TError = ErrorType<HTTPValidationError>,
>(
	storeId: number | undefined | null,
	headers?: GetFavoritesHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getFavorites>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetFavoritesSuspenseQueryOptions(storeId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Toggle Product Favorite
 */
export const toggleProductFavorite = (
	storeId: number | undefined | null,
	productId: number | undefined | null,
	headers?: ToggleProductFavoriteHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ToggleFavoriteProductResult>({
		url: `/store/favorites/${storeId}/${productId}/toggle`,
		method: "POST",
		headers,
		signal,
	});
};

export const getToggleProductFavoriteMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof toggleProductFavorite>>,
		TError,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			headers?: ToggleProductFavoriteHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof toggleProductFavorite>>,
	TError,
	{
		storeId: number | undefined | null;
		productId: number | undefined | null;
		headers?: ToggleProductFavoriteHeaders;
	},
	TContext
> => {
	const mutationKey = ["toggleProductFavorite"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof toggleProductFavorite>>,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			headers?: ToggleProductFavoriteHeaders;
		}
	> = props => {
		const { storeId, productId, headers } = props ?? {};

		return toggleProductFavorite(storeId, productId, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ToggleProductFavoriteMutationResult = NonNullable<
	Awaited<ReturnType<typeof toggleProductFavorite>>
>;

export type ToggleProductFavoriteMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Toggle Product Favorite
 */
export const useToggleProductFavorite = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof toggleProductFavorite>>,
		TError,
		{
			storeId: number | undefined | null;
			productId: number | undefined | null;
			headers?: ToggleProductFavoriteHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof toggleProductFavorite>>,
	TError,
	{
		storeId: number | undefined | null;
		productId: number | undefined | null;
		headers?: ToggleProductFavoriteHeaders;
	},
	TContext
> => {
	const mutationOptions = getToggleProductFavoriteMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get receipt by id
 * @summary Get Receipt
 */
export const getReceipt = (
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ReceiptSchema>({
		url: `/store/scan/${receiptId}`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetReceiptQueryKey = (
	receiptId: number | undefined | null,
	params?: GetReceiptParams
) => {
	return [`/store/scan/${receiptId}`, ...(params ? [params] : [])] as const;
};

export const getGetReceiptQueryOptions = <
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReceiptQueryKey(receiptId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReceipt>>> = ({ signal }) =>
		getReceipt(receiptId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!receiptId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getReceipt>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReceiptQueryResult = NonNullable<Awaited<ReturnType<typeof getReceipt>>>;
export type GetReceiptQueryError = ErrorType<HTTPValidationError>;

export function useGetReceipt<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params: undefined | GetReceiptParams,
	headers: undefined | GetReceiptHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReceipt>>,
					TError,
					Awaited<ReturnType<typeof getReceipt>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReceipt<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReceipt>>,
					TError,
					Awaited<ReturnType<typeof getReceipt>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReceipt<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Receipt
 */

export function useGetReceipt<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReceiptQueryOptions(receiptId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetReceiptSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReceiptQueryKey(receiptId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReceipt>>> = ({ signal }) =>
		getReceipt(receiptId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getReceipt>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReceiptSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getReceipt>>>;
export type GetReceiptSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetReceiptSuspense<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params: undefined | GetReceiptParams,
	headers: undefined | GetReceiptHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReceiptSuspense<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReceiptSuspense<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Receipt
 */

export function useGetReceiptSuspense<
	TData = Awaited<ReturnType<typeof getReceipt>>,
	TError = ErrorType<HTTPValidationError>,
>(
	receiptId: number | undefined | null,
	params?: GetReceiptParams,
	headers?: GetReceiptHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReceipt>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReceiptSuspenseQueryOptions(receiptId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to process and save scanned receipt
 * @summary Scan And Save Receipt
 */
export const scanAndSaveReceipt = (
	scanPayloadSchema: ScanPayloadSchema,
	headers?: ScanAndSaveReceiptHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ScanResponseSchema>({
		url: `/store/scan/scan_and_save`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: scanPayloadSchema,
		signal,
	});
};

export const getScanAndSaveReceiptMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof scanAndSaveReceipt>>,
		TError,
		{ data: ScanPayloadSchema; headers?: ScanAndSaveReceiptHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof scanAndSaveReceipt>>,
	TError,
	{ data: ScanPayloadSchema; headers?: ScanAndSaveReceiptHeaders },
	TContext
> => {
	const mutationKey = ["scanAndSaveReceipt"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof scanAndSaveReceipt>>,
		{ data: ScanPayloadSchema; headers?: ScanAndSaveReceiptHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return scanAndSaveReceipt(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ScanAndSaveReceiptMutationResult = NonNullable<
	Awaited<ReturnType<typeof scanAndSaveReceipt>>
>;
export type ScanAndSaveReceiptMutationBody = ScanPayloadSchema;
export type ScanAndSaveReceiptMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Scan And Save Receipt
 */
export const useScanAndSaveReceipt = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof scanAndSaveReceipt>>,
		TError,
		{ data: ScanPayloadSchema; headers?: ScanAndSaveReceiptHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof scanAndSaveReceipt>>,
	TError,
	{ data: ScanPayloadSchema; headers?: ScanAndSaveReceiptHeaders },
	TContext
> => {
	const mutationOptions = getScanAndSaveReceiptMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to process scanned receipt
 * @summary Scan Receipt
 */
export const scanReceipt = (
	scanPayloadSchema: ScanPayloadSchema,
	headers?: ScanReceiptHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ScanResponseUnAuthSchema>({
		url: `/store/scan/`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: scanPayloadSchema,
		signal,
	});
};

export const getScanReceiptMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof scanReceipt>>,
		TError,
		{ data: ScanPayloadSchema; headers?: ScanReceiptHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof scanReceipt>>,
	TError,
	{ data: ScanPayloadSchema; headers?: ScanReceiptHeaders },
	TContext
> => {
	const mutationKey = ["scanReceipt"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof scanReceipt>>,
		{ data: ScanPayloadSchema; headers?: ScanReceiptHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return scanReceipt(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ScanReceiptMutationResult = NonNullable<Awaited<ReturnType<typeof scanReceipt>>>;
export type ScanReceiptMutationBody = ScanPayloadSchema;
export type ScanReceiptMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Scan Receipt
 */
export const useScanReceipt = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof scanReceipt>>,
		TError,
		{ data: ScanPayloadSchema; headers?: ScanReceiptHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof scanReceipt>>,
	TError,
	{ data: ScanPayloadSchema; headers?: ScanReceiptHeaders },
	TContext
> => {
	const mutationOptions = getScanReceiptMutationOptions(options);

	return useMutation(mutationOptions);
};
