/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Client API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	AcceptInvitationHeaders,
	AddCouponToWalletHeaders,
	CheckCouponHeaders,
	CouponsAddedResponse,
	DeleteCouponHeaders,
	GetCouponByIdHeaders,
	GetCouponInfoHeaders,
	GetGiftCardByIdHeaders,
	GetIncustAllUserCouponsHeaders,
	GetIncustAllUserCouponsParams,
	GetIncustSettingsByContextHeaders,
	GetIncustSettingsByContextParams,
	GetIncustSettingsHeaders,
	GetIncustSettingsParams,
	GetIncustUserCardImageHeaders,
	GetIncustUserCardImageParams,
	GetIncustUserCardQrHeaders,
	GetIncustUserCardQrParams,
	GetInvitationInfoHeaders,
	GetLoyaltySettingsHeaders,
	GetReferralChainHeaders,
	GetReferralChainParams,
	GetReferralCodeHeaders,
	GetReferralSummaryHeaders,
	GetSpecialAccountsHeaders,
	GetSpecialAccountsMapping200,
	GetSpecialAccountsMappingHeaders,
	GetSpecialAccountsMappingParams,
	GetSpecialAccountsParams,
	GetTransactionHeaders,
	GetUserCouponsHeaders,
	GetUserCouponsParams,
	GetUserWalletHeaders,
	GetWlclientCouponsByIdsHeaders,
	GetWlclientCouponsByIdsParams,
	GiftCard,
	HTTPValidationError,
	InCustMessage,
	IncustClientApiClientModelsCouponCoupon,
	IncustClientApiClientModelsLoyaltySettingsLoyaltySettings,
	IncustClientApiClientModelsTransactionTransaction,
	IncustCouponExt,
	IncustTerminalApiClientModelsCheckCheck,
	IncustTerminalApiClientModelsCouponCoupon,
	IncustTerminalApiClientModelsSpecialAccountSpecialAccount,
	IncustUserCardQrSchema,
	IncustWalletsExt,
	LoyaltySettingsSchema,
	MessageResponse,
	ProcessCheckHeaders,
	ProcessCouponHeaders,
	ProcessCouponParams,
	ProcessIncustCheckPayloadSchema,
	RedeemGiftCardHeaders,
	RedeemVoucherHeaders,
	RedeemVoucherTermHeaders,
	ReferralProgramChain,
	ReferralProgramCode,
	ReferralProgramCodeInfo,
	ReferralProgramSummary,
	SendIncustMessageHeaders,
	Settings,
	ShareCouponHeaders,
	StorePayloadBaseSchema,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * Method to send incust message
 * @summary Send Incust Message
 */
export const sendIncustMessage = (
	inCustMessage: InCustMessage,
	headers?: SendIncustMessageHeaders,
	signal?: AbortSignal
) => {
	return getAxios<unknown>({
		url: `/external/incust/message`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: inCustMessage,
		signal,
	});
};

export const getSendIncustMessageMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendIncustMessage>>,
		TError,
		{ data: InCustMessage; headers?: SendIncustMessageHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof sendIncustMessage>>,
	TError,
	{ data: InCustMessage; headers?: SendIncustMessageHeaders },
	TContext
> => {
	const mutationKey = ["sendIncustMessage"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof sendIncustMessage>>,
		{ data: InCustMessage; headers?: SendIncustMessageHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return sendIncustMessage(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type SendIncustMessageMutationResult = NonNullable<
	Awaited<ReturnType<typeof sendIncustMessage>>
>;
export type SendIncustMessageMutationBody = InCustMessage;
export type SendIncustMessageMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Send Incust Message
 */
export const useSendIncustMessage = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof sendIncustMessage>>,
		TError,
		{ data: InCustMessage; headers?: SendIncustMessageHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof sendIncustMessage>>,
	TError,
	{ data: InCustMessage; headers?: SendIncustMessageHeaders },
	TContext
> => {
	const mutationOptions = getSendIncustMessageMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to process incust check
 * @summary Process Check
 */
export const processCheck = (
	brandId: number | undefined | null,
	processIncustCheckPayloadSchema: ProcessIncustCheckPayloadSchema,
	headers?: ProcessCheckHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustTerminalApiClientModelsCheckCheck>({
		url: `/external/incust/process_check/${brandId}`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: processIncustCheckPayloadSchema,
		signal,
	});
};

export const getProcessCheckMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof processCheck>>,
		TError,
		{
			brandId: number | undefined | null;
			data: ProcessIncustCheckPayloadSchema;
			headers?: ProcessCheckHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof processCheck>>,
	TError,
	{
		brandId: number | undefined | null;
		data: ProcessIncustCheckPayloadSchema;
		headers?: ProcessCheckHeaders;
	},
	TContext
> => {
	const mutationKey = ["processCheck"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof processCheck>>,
		{
			brandId: number | undefined | null;
			data: ProcessIncustCheckPayloadSchema;
			headers?: ProcessCheckHeaders;
		}
	> = props => {
		const { brandId, data, headers } = props ?? {};

		return processCheck(brandId, data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ProcessCheckMutationResult = NonNullable<Awaited<ReturnType<typeof processCheck>>>;
export type ProcessCheckMutationBody = ProcessIncustCheckPayloadSchema;
export type ProcessCheckMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Process Check
 */
export const useProcessCheck = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof processCheck>>,
		TError,
		{
			brandId: number | undefined | null;
			data: ProcessIncustCheckPayloadSchema;
			headers?: ProcessCheckHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof processCheck>>,
	TError,
	{
		brandId: number | undefined | null;
		data: ProcessIncustCheckPayloadSchema;
		headers?: ProcessCheckHeaders;
	},
	TContext
> => {
	const mutationOptions = getProcessCheckMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get incust settings
 * @summary Get Incust Settings
 */
export const getIncustSettings = (
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<Settings>({
		url: `/external/incust/settings/${brandId}`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustSettingsQueryKey = (
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams
) => {
	return [`/external/incust/settings/${brandId}`, ...(params ? [params] : [])] as const;
};

export const getGetIncustSettingsQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustSettingsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustSettings>>> = ({ signal }) =>
		getIncustSettings(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustSettings>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustSettingsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustSettings>>
>;
export type GetIncustSettingsQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustSettings<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetIncustSettingsParams,
	headers: undefined | GetIncustSettingsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustSettings>>,
					TError,
					Awaited<ReturnType<typeof getIncustSettings>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettings<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustSettings>>,
					TError,
					Awaited<ReturnType<typeof getIncustSettings>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettings<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Settings
 */

export function useGetIncustSettings<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustSettingsQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustSettingsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustSettingsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustSettings>>> = ({ signal }) =>
		getIncustSettings(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustSettings>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustSettingsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustSettings>>
>;
export type GetIncustSettingsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustSettingsSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetIncustSettingsParams,
	headers: undefined | GetIncustSettingsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Settings
 */

export function useGetIncustSettingsSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustSettingsParams,
	headers?: GetIncustSettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustSettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustSettingsSuspenseQueryOptions(
		brandId,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust user qr card
 * @summary Get Incust User Card Qr
 */
export const getIncustUserCardQr = (
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustUserCardQrSchema>({
		url: `/external/incust/user_qr_card/${brandId}`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustUserCardQrQueryKey = (
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams
) => {
	return [`/external/incust/user_qr_card/${brandId}`, ...(params ? [params] : [])] as const;
};

export const getGetIncustUserCardQrQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustUserCardQrQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustUserCardQr>>> = ({ signal }) =>
		getIncustUserCardQr(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustUserCardQr>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustUserCardQrQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustUserCardQr>>
>;
export type GetIncustUserCardQrQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustUserCardQr<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetIncustUserCardQrParams,
	headers: undefined | GetIncustUserCardQrHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustUserCardQr>>,
					TError,
					Awaited<ReturnType<typeof getIncustUserCardQr>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardQr<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustUserCardQr>>,
					TError,
					Awaited<ReturnType<typeof getIncustUserCardQr>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardQr<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust User Card Qr
 */

export function useGetIncustUserCardQr<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustUserCardQrQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustUserCardQrSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustUserCardQrQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustUserCardQr>>> = ({ signal }) =>
		getIncustUserCardQr(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustUserCardQr>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustUserCardQrSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustUserCardQr>>
>;
export type GetIncustUserCardQrSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustUserCardQrSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetIncustUserCardQrParams,
	headers: undefined | GetIncustUserCardQrHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardQrSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardQrSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust User Card Qr
 */

export function useGetIncustUserCardQrSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardQr>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetIncustUserCardQrParams,
	headers?: GetIncustUserCardQrHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardQr>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustUserCardQrSuspenseQueryOptions(
		brandId,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust user card image
 * @summary Get Incust User Card Image
 */
export const getIncustUserCardImage = (
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	signal?: AbortSignal
) => {
	return getAxios<void>({
		url: `/external/incust/${groupId}/card_image`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustUserCardImageQueryKey = (
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams
) => {
	return [`/external/incust/${groupId}/card_image`, ...(params ? [params] : [])] as const;
};

export const getGetIncustUserCardImageQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardImage>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustUserCardImageQueryKey(groupId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustUserCardImage>>> = ({
		signal,
	}) => getIncustUserCardImage(groupId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!groupId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustUserCardImage>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustUserCardImageQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustUserCardImage>>
>;
export type GetIncustUserCardImageQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustUserCardImage<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers: undefined | GetIncustUserCardImageHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardImage>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustUserCardImage>>,
					TError,
					Awaited<ReturnType<typeof getIncustUserCardImage>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardImage<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardImage>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustUserCardImage>>,
					TError,
					Awaited<ReturnType<typeof getIncustUserCardImage>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardImage<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardImage>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust User Card Image
 */

export function useGetIncustUserCardImage<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustUserCardImage>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustUserCardImageQueryOptions(groupId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustUserCardImageSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustUserCardImage>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustUserCardImageQueryKey(groupId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustUserCardImage>>> = ({
		signal,
	}) => getIncustUserCardImage(groupId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustUserCardImage>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustUserCardImageSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustUserCardImage>>
>;
export type GetIncustUserCardImageSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustUserCardImageSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers: undefined | GetIncustUserCardImageHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustUserCardImage>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardImageSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustUserCardImage>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustUserCardImageSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustUserCardImage>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust User Card Image
 */

export function useGetIncustUserCardImageSuspense<
	TData = Awaited<ReturnType<typeof getIncustUserCardImage>>,
	TError = ErrorType<HTTPValidationError>,
>(
	groupId: number | undefined | null,
	params: GetIncustUserCardImageParams,
	headers?: GetIncustUserCardImageHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustUserCardImage>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustUserCardImageSuspenseQueryOptions(
		groupId,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust coupon info
 * @summary Get Coupon Info
 */
export const getCouponInfo = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsCouponCoupon>({
		url: `/external/incust/get_coupon_info/${brandId}/${couponId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetCouponInfoQueryKey = (
	brandId: number | undefined | null,
	couponId: string | undefined | null
) => {
	return [`/external/incust/get_coupon_info/${brandId}/${couponId}`] as const;
};

export const getGetCouponInfoQueryOptions = <
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCouponInfoQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCouponInfo>>> = ({ signal }) =>
		getCouponInfo(brandId, couponId, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(brandId && couponId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetCouponInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getCouponInfo>>>;
export type GetCouponInfoQueryError = ErrorType<HTTPValidationError>;

export function useGetCouponInfo<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | GetCouponInfoHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCouponInfo>>,
					TError,
					Awaited<ReturnType<typeof getCouponInfo>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponInfo<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCouponInfo>>,
					TError,
					Awaited<ReturnType<typeof getCouponInfo>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponInfo<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Coupon Info
 */

export function useGetCouponInfo<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCouponInfoQueryOptions(brandId, couponId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCouponInfoSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCouponInfoQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCouponInfo>>> = ({ signal }) =>
		getCouponInfo(brandId, couponId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCouponInfo>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCouponInfoSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCouponInfo>>
>;
export type GetCouponInfoSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCouponInfoSuspense<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | GetCouponInfoHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponInfoSuspense<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponInfoSuspense<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Coupon Info
 */

export function useGetCouponInfoSuspense<
	TData = Awaited<ReturnType<typeof getCouponInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCouponInfoSuspenseQueryOptions(brandId, couponId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust coupon info by ID
 * @summary Get Coupon By Id
 */
export const getCouponById = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsCouponCoupon>({
		url: `/external/incust/${brandId}/coupons/${couponId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetCouponByIdQueryKey = (
	brandId: number | undefined | null,
	couponId: string | undefined | null
) => {
	return [`/external/incust/${brandId}/coupons/${couponId}`] as const;
};

export const getGetCouponByIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCouponByIdQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCouponById>>> = ({ signal }) =>
		getCouponById(brandId, couponId, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(brandId && couponId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetCouponByIdQueryResult = NonNullable<Awaited<ReturnType<typeof getCouponById>>>;
export type GetCouponByIdQueryError = ErrorType<HTTPValidationError>;

export function useGetCouponById<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | GetCouponByIdHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCouponById>>,
					TError,
					Awaited<ReturnType<typeof getCouponById>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponById<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getCouponById>>,
					TError,
					Awaited<ReturnType<typeof getCouponById>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponById<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Coupon By Id
 */

export function useGetCouponById<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCouponByIdQueryOptions(brandId, couponId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetCouponByIdSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetCouponByIdQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getCouponById>>> = ({ signal }) =>
		getCouponById(brandId, couponId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getCouponById>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetCouponByIdSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getCouponById>>
>;
export type GetCouponByIdSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetCouponByIdSuspense<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | GetCouponByIdHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponByIdSuspense<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetCouponByIdSuspense<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Coupon By Id
 */

export function useGetCouponByIdSuspense<
	TData = Awaited<ReturnType<typeof getCouponById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: GetCouponByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getCouponById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetCouponByIdSuspenseQueryOptions(brandId, couponId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to delete coupon
 * @summary Delete Coupon
 */
export const deleteCoupon = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: DeleteCouponHeaders
) => {
	return getAxios<MessageResponse>({
		url: `/external/incust/${brandId}/coupons/${couponId}`,
		method: "DELETE",
		headers,
	});
};

export const getDeleteCouponMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteCoupon>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: DeleteCouponHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof deleteCoupon>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: DeleteCouponHeaders;
	},
	TContext
> => {
	const mutationKey = ["deleteCoupon"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteCoupon>>,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: DeleteCouponHeaders;
		}
	> = props => {
		const { brandId, couponId, headers } = props ?? {};

		return deleteCoupon(brandId, couponId, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteCouponMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCoupon>>>;

export type DeleteCouponMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Delete Coupon
 */
export const useDeleteCoupon = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof deleteCoupon>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: DeleteCouponHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof deleteCoupon>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: DeleteCouponHeaders;
	},
	TContext
> => {
	const mutationOptions = getDeleteCouponMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to add coupon to user wallet
 * @summary Add Coupon To Wallet
 */
export const addCouponToWallet = (
	brandId: number | undefined | null,
	couponCode: string | undefined | null,
	storePayloadBaseSchema: StorePayloadBaseSchema,
	headers?: AddCouponToWalletHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CouponsAddedResponse>({
		url: `/external/incust/add_coupon_to_wallet/${brandId}/${couponCode}`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: storePayloadBaseSchema,
		signal,
	});
};

export const getAddCouponToWalletMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addCouponToWallet>>,
		TError,
		{
			brandId: number | undefined | null;
			couponCode: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AddCouponToWalletHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof addCouponToWallet>>,
	TError,
	{
		brandId: number | undefined | null;
		couponCode: string | undefined | null;
		data: StorePayloadBaseSchema;
		headers?: AddCouponToWalletHeaders;
	},
	TContext
> => {
	const mutationKey = ["addCouponToWallet"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof addCouponToWallet>>,
		{
			brandId: number | undefined | null;
			couponCode: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AddCouponToWalletHeaders;
		}
	> = props => {
		const { brandId, couponCode, data, headers } = props ?? {};

		return addCouponToWallet(brandId, couponCode, data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type AddCouponToWalletMutationResult = NonNullable<
	Awaited<ReturnType<typeof addCouponToWallet>>
>;
export type AddCouponToWalletMutationBody = StorePayloadBaseSchema;
export type AddCouponToWalletMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Add Coupon To Wallet
 */
export const useAddCouponToWallet = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof addCouponToWallet>>,
		TError,
		{
			brandId: number | undefined | null;
			couponCode: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AddCouponToWalletHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof addCouponToWallet>>,
	TError,
	{
		brandId: number | undefined | null;
		couponCode: string | undefined | null;
		data: StorePayloadBaseSchema;
		headers?: AddCouponToWalletHeaders;
	},
	TContext
> => {
	const mutationOptions = getAddCouponToWalletMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to redeem coupon
 * @summary Redeem Voucher
 */
export const redeemVoucher = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: RedeemVoucherHeaders,
	signal?: AbortSignal
) => {
	return getAxios<MessageResponse>({
		url: `/external/incust/redeem/${brandId}/${couponId}`,
		method: "POST",
		headers,
		signal,
	});
};

export const getRedeemVoucherMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemVoucher>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof redeemVoucher>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: RedeemVoucherHeaders;
	},
	TContext
> => {
	const mutationKey = ["redeemVoucher"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof redeemVoucher>>,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherHeaders;
		}
	> = props => {
		const { brandId, couponId, headers } = props ?? {};

		return redeemVoucher(brandId, couponId, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type RedeemVoucherMutationResult = NonNullable<Awaited<ReturnType<typeof redeemVoucher>>>;

export type RedeemVoucherMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Redeem Voucher
 */
export const useRedeemVoucher = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemVoucher>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof redeemVoucher>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: RedeemVoucherHeaders;
	},
	TContext
> => {
	const mutationOptions = getRedeemVoucherMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to redeem coupon via terminal
 * @summary Redeem Voucher Term
 */
export const redeemVoucherTerm = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: RedeemVoucherTermHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustTerminalApiClientModelsCouponCoupon[]>({
		url: `/external/incust/redeem_term/${brandId}/${couponId}`,
		method: "POST",
		headers,
		signal,
	});
};

export const getRedeemVoucherTermMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemVoucherTerm>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherTermHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof redeemVoucherTerm>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: RedeemVoucherTermHeaders;
	},
	TContext
> => {
	const mutationKey = ["redeemVoucherTerm"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof redeemVoucherTerm>>,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherTermHeaders;
		}
	> = props => {
		const { brandId, couponId, headers } = props ?? {};

		return redeemVoucherTerm(brandId, couponId, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type RedeemVoucherTermMutationResult = NonNullable<
	Awaited<ReturnType<typeof redeemVoucherTerm>>
>;

export type RedeemVoucherTermMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Redeem Voucher Term
 */
export const useRedeemVoucherTerm = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemVoucherTerm>>,
		TError,
		{
			brandId: number | undefined | null;
			couponId: string | undefined | null;
			headers?: RedeemVoucherTermHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof redeemVoucherTerm>>,
	TError,
	{
		brandId: number | undefined | null;
		couponId: string | undefined | null;
		headers?: RedeemVoucherTermHeaders;
	},
	TContext
> => {
	const mutationOptions = getRedeemVoucherTermMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get incust referral code
 * @summary Get Referral Code
 */
export const getReferralCode = (
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ReferralProgramCode>({
		url: `/external/incust/get_referral_code/${brandId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetReferralCodeQueryKey = (brandId: number | undefined | null) => {
	return [`/external/incust/get_referral_code/${brandId}`] as const;
};

export const getGetReferralCodeQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralCodeQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralCode>>> = ({ signal }) =>
		getReferralCode(brandId, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getReferralCode>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralCodeQueryResult = NonNullable<Awaited<ReturnType<typeof getReferralCode>>>;
export type GetReferralCodeQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralCode<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetReferralCodeHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralCode>>,
					TError,
					Awaited<ReturnType<typeof getReferralCode>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralCode<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralCode>>,
					TError,
					Awaited<ReturnType<typeof getReferralCode>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralCode<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Code
 */

export function useGetReferralCode<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralCodeQueryOptions(brandId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetReferralCodeSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralCodeQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralCode>>> = ({ signal }) =>
		getReferralCode(brandId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getReferralCode>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralCodeSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getReferralCode>>
>;
export type GetReferralCodeSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralCodeSuspense<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetReferralCodeHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralCodeSuspense<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralCodeSuspense<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Code
 */

export function useGetReferralCodeSuspense<
	TData = Awaited<ReturnType<typeof getReferralCode>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralCodeHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralCode>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralCodeSuspenseQueryOptions(brandId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to accept invitation
 * @summary Accept Invitation
 */
export const acceptInvitation = (
	brandId: number | undefined | null,
	code: string | undefined | null,
	storePayloadBaseSchema: StorePayloadBaseSchema,
	headers?: AcceptInvitationHeaders,
	signal?: AbortSignal
) => {
	return getAxios<MessageResponse>({
		url: `/external/incust/accept_invitation/${brandId}/${code}`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: storePayloadBaseSchema,
		signal,
	});
};

export const getAcceptInvitationMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof acceptInvitation>>,
		TError,
		{
			brandId: number | undefined | null;
			code: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AcceptInvitationHeaders;
		},
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof acceptInvitation>>,
	TError,
	{
		brandId: number | undefined | null;
		code: string | undefined | null;
		data: StorePayloadBaseSchema;
		headers?: AcceptInvitationHeaders;
	},
	TContext
> => {
	const mutationKey = ["acceptInvitation"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof acceptInvitation>>,
		{
			brandId: number | undefined | null;
			code: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AcceptInvitationHeaders;
		}
	> = props => {
		const { brandId, code, data, headers } = props ?? {};

		return acceptInvitation(brandId, code, data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type AcceptInvitationMutationResult = NonNullable<
	Awaited<ReturnType<typeof acceptInvitation>>
>;
export type AcceptInvitationMutationBody = StorePayloadBaseSchema;
export type AcceptInvitationMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Accept Invitation
 */
export const useAcceptInvitation = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof acceptInvitation>>,
		TError,
		{
			brandId: number | undefined | null;
			code: string | undefined | null;
			data: StorePayloadBaseSchema;
			headers?: AcceptInvitationHeaders;
		},
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof acceptInvitation>>,
	TError,
	{
		brandId: number | undefined | null;
		code: string | undefined | null;
		data: StorePayloadBaseSchema;
		headers?: AcceptInvitationHeaders;
	},
	TContext
> => {
	const mutationOptions = getAcceptInvitationMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get incust loyalty settings
 * @summary Get Loyalty Settings
 */
export const getLoyaltySettings = (headers?: GetLoyaltySettingsHeaders, signal?: AbortSignal) => {
	return getAxios<IncustClientApiClientModelsLoyaltySettingsLoyaltySettings>({
		url: `/external/incust/loyalty_settings`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetLoyaltySettingsQueryKey = () => {
	return [`/external/incust/loyalty_settings`] as const;
};

export const getGetLoyaltySettingsQueryOptions = <
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetLoyaltySettingsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getLoyaltySettings>>> = ({ signal }) =>
		getLoyaltySettings(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getLoyaltySettings>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetLoyaltySettingsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getLoyaltySettings>>
>;
export type GetLoyaltySettingsQueryError = ErrorType<HTTPValidationError>;

export function useGetLoyaltySettings<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetLoyaltySettingsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getLoyaltySettings>>,
					TError,
					Awaited<ReturnType<typeof getLoyaltySettings>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLoyaltySettings<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getLoyaltySettings>>,
					TError,
					Awaited<ReturnType<typeof getLoyaltySettings>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLoyaltySettings<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Loyalty Settings
 */

export function useGetLoyaltySettings<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetLoyaltySettingsQueryOptions(headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetLoyaltySettingsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetLoyaltySettingsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getLoyaltySettings>>> = ({ signal }) =>
		getLoyaltySettings(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getLoyaltySettings>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetLoyaltySettingsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getLoyaltySettings>>
>;
export type GetLoyaltySettingsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetLoyaltySettingsSuspense<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetLoyaltySettingsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLoyaltySettingsSuspense<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLoyaltySettingsSuspense<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Loyalty Settings
 */

export function useGetLoyaltySettingsSuspense<
	TData = Awaited<ReturnType<typeof getLoyaltySettings>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetLoyaltySettingsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLoyaltySettings>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetLoyaltySettingsSuspenseQueryOptions(headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust invitation info
 * @summary Get Invitation Info
 */
export const getInvitationInfo = (
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ReferralProgramCodeInfo>({
		url: `/external/incust/invitation/${brandId}/${code}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetInvitationInfoQueryKey = (
	brandId: number | undefined | null,
	code: string | undefined | null
) => {
	return [`/external/incust/invitation/${brandId}/${code}`] as const;
};

export const getGetInvitationInfoQueryOptions = <
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetInvitationInfoQueryKey(brandId, code);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getInvitationInfo>>> = ({ signal }) =>
		getInvitationInfo(brandId, code, headers, signal);

	return { queryKey, queryFn, enabled: !!(brandId && code), ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getInvitationInfo>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInvitationInfoQueryResult = NonNullable<
	Awaited<ReturnType<typeof getInvitationInfo>>
>;
export type GetInvitationInfoQueryError = ErrorType<HTTPValidationError>;

export function useGetInvitationInfo<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers: undefined | GetInvitationInfoHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getInvitationInfo>>,
					TError,
					Awaited<ReturnType<typeof getInvitationInfo>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetInvitationInfo<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getInvitationInfo>>,
					TError,
					Awaited<ReturnType<typeof getInvitationInfo>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetInvitationInfo<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Invitation Info
 */

export function useGetInvitationInfo<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetInvitationInfoQueryOptions(brandId, code, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetInvitationInfoSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetInvitationInfoQueryKey(brandId, code);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getInvitationInfo>>> = ({ signal }) =>
		getInvitationInfo(brandId, code, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getInvitationInfo>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInvitationInfoSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getInvitationInfo>>
>;
export type GetInvitationInfoSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetInvitationInfoSuspense<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers: undefined | GetInvitationInfoHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetInvitationInfoSuspense<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetInvitationInfoSuspense<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Invitation Info
 */

export function useGetInvitationInfoSuspense<
	TData = Awaited<ReturnType<typeof getInvitationInfo>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	code: string | undefined | null,
	headers?: GetInvitationInfoHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getInvitationInfo>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetInvitationInfoSuspenseQueryOptions(brandId, code, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust user referral summary
 * @summary Get Referral Summary
 */
export const getReferralSummary = (
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ReferralProgramSummary>({
		url: `/external/incust/referral_summary/${brandId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetReferralSummaryQueryKey = (brandId: number | undefined | null) => {
	return [`/external/incust/referral_summary/${brandId}`] as const;
};

export const getGetReferralSummaryQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralSummaryQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralSummary>>> = ({ signal }) =>
		getReferralSummary(brandId, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getReferralSummary>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralSummaryQueryResult = NonNullable<
	Awaited<ReturnType<typeof getReferralSummary>>
>;
export type GetReferralSummaryQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralSummary<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetReferralSummaryHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralSummary>>,
					TError,
					Awaited<ReturnType<typeof getReferralSummary>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralSummary<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralSummary>>,
					TError,
					Awaited<ReturnType<typeof getReferralSummary>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralSummary<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Summary
 */

export function useGetReferralSummary<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralSummaryQueryOptions(brandId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetReferralSummarySuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralSummaryQueryKey(brandId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralSummary>>> = ({ signal }) =>
		getReferralSummary(brandId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getReferralSummary>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralSummarySuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getReferralSummary>>
>;
export type GetReferralSummarySuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralSummarySuspense<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers: undefined | GetReferralSummaryHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralSummarySuspense<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralSummarySuspense<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Summary
 */

export function useGetReferralSummarySuspense<
	TData = Awaited<ReturnType<typeof getReferralSummary>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	headers?: GetReferralSummaryHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralSummary>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralSummarySuspenseQueryOptions(brandId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust user referral chain
 * @summary Get Referral Chain
 */
export const getReferralChain = (
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	signal?: AbortSignal
) => {
	return getAxios<ReferralProgramChain>({
		url: `/external/incust/referral_chain/${brandId}`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetReferralChainQueryKey = (
	brandId: number | undefined | null,
	params?: GetReferralChainParams
) => {
	return [`/external/incust/referral_chain/${brandId}`, ...(params ? [params] : [])] as const;
};

export const getGetReferralChainQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralChainQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralChain>>> = ({ signal }) =>
		getReferralChain(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getReferralChain>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralChainQueryResult = NonNullable<Awaited<ReturnType<typeof getReferralChain>>>;
export type GetReferralChainQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralChain<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetReferralChainParams,
	headers: undefined | GetReferralChainHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralChain>>,
					TError,
					Awaited<ReturnType<typeof getReferralChain>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralChain<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getReferralChain>>,
					TError,
					Awaited<ReturnType<typeof getReferralChain>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralChain<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Chain
 */

export function useGetReferralChain<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralChainQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetReferralChainSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetReferralChainQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralChain>>> = ({ signal }) =>
		getReferralChain(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getReferralChain>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetReferralChainSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getReferralChain>>
>;
export type GetReferralChainSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetReferralChainSuspense<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetReferralChainParams,
	headers: undefined | GetReferralChainHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralChainSuspense<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetReferralChainSuspense<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Referral Chain
 */

export function useGetReferralChainSuspense<
	TData = Awaited<ReturnType<typeof getReferralChain>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetReferralChainParams,
	headers?: GetReferralChainHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getReferralChain>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetReferralChainSuspenseQueryOptions(brandId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust user coupons
 * @summary Get User Coupons
 */
export const getUserCoupons = (
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsCouponCoupon[]>({
		url: `/external/incust/${brandId}/coupons`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetUserCouponsQueryKey = (
	brandId: number | undefined | null,
	params?: GetUserCouponsParams
) => {
	return [`/external/incust/${brandId}/coupons`, ...(params ? [params] : [])] as const;
};

export const getGetUserCouponsQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserCouponsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserCoupons>>> = ({ signal }) =>
		getUserCoupons(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUserCoupons>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserCouponsQueryResult = NonNullable<Awaited<ReturnType<typeof getUserCoupons>>>;
export type GetUserCouponsQueryError = ErrorType<HTTPValidationError>;

export function useGetUserCoupons<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetUserCouponsParams,
	headers: undefined | GetUserCouponsHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserCoupons>>,
					TError,
					Awaited<ReturnType<typeof getUserCoupons>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserCoupons<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserCoupons>>,
					TError,
					Awaited<ReturnType<typeof getUserCoupons>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserCoupons<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Coupons
 */

export function useGetUserCoupons<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserCouponsQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserCouponsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserCouponsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserCoupons>>> = ({ signal }) =>
		getUserCoupons(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUserCoupons>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserCouponsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserCoupons>>
>;
export type GetUserCouponsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: undefined | GetUserCouponsParams,
	headers: undefined | GetUserCouponsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Coupons
 */

export function useGetUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params?: GetUserCouponsParams,
	headers?: GetUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserCoupons>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserCouponsSuspenseQueryOptions(brandId, params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust check (transaction)
 * @summary Get Transaction
 */
export const getTransaction = (
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsTransactionTransaction>({
		url: `/external/incust/${brandId}/transactions/${transactionId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetTransactionQueryKey = (
	brandId: number | undefined | null,
	transactionId: string | undefined | null
) => {
	return [`/external/incust/${brandId}/transactions/${transactionId}`] as const;
};

export const getGetTransactionQueryOptions = <
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTransactionQueryKey(brandId, transactionId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTransaction>>> = ({ signal }) =>
		getTransaction(brandId, transactionId, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(brandId && transactionId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type GetTransactionQueryResult = NonNullable<Awaited<ReturnType<typeof getTransaction>>>;
export type GetTransactionQueryError = ErrorType<HTTPValidationError>;

export function useGetTransaction<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers: undefined | GetTransactionHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getTransaction>>,
					TError,
					Awaited<ReturnType<typeof getTransaction>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTransaction<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getTransaction>>,
					TError,
					Awaited<ReturnType<typeof getTransaction>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTransaction<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Transaction
 */

export function useGetTransaction<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetTransactionQueryOptions(brandId, transactionId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetTransactionSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTransactionQueryKey(brandId, transactionId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTransaction>>> = ({ signal }) =>
		getTransaction(brandId, transactionId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getTransaction>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetTransactionSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getTransaction>>
>;
export type GetTransactionSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetTransactionSuspense<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers: undefined | GetTransactionHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTransactionSuspense<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetTransactionSuspense<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Transaction
 */

export function useGetTransactionSuspense<
	TData = Awaited<ReturnType<typeof getTransaction>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	transactionId: string | undefined | null,
	headers?: GetTransactionHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getTransaction>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetTransactionSuspenseQueryOptions(
		brandId,
		transactionId,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to share coupon
 * @summary Share Coupon
 */
export const shareCoupon = (
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	signal?: AbortSignal
) => {
	return getAxios<MessageResponse>({
		url: `/external/incust/${brandId}/share/${couponId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getShareCouponQueryKey = (
	brandId: number | undefined | null,
	couponId: string | undefined | null
) => {
	return [`/external/incust/${brandId}/share/${couponId}`] as const;
};

export const getShareCouponQueryOptions = <
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getShareCouponQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof shareCoupon>>> = ({ signal }) =>
		shareCoupon(brandId, couponId, headers, signal);

	return {
		queryKey,
		queryFn,
		enabled: !!(brandId && couponId),
		...queryOptions,
	} as UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};
};

export type ShareCouponQueryResult = NonNullable<Awaited<ReturnType<typeof shareCoupon>>>;
export type ShareCouponQueryError = ErrorType<HTTPValidationError>;

export function useShareCoupon<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | ShareCouponHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof shareCoupon>>,
					TError,
					Awaited<ReturnType<typeof shareCoupon>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useShareCoupon<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof shareCoupon>>,
					TError,
					Awaited<ReturnType<typeof shareCoupon>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useShareCoupon<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Share Coupon
 */

export function useShareCoupon<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getShareCouponQueryOptions(brandId, couponId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getShareCouponSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getShareCouponQueryKey(brandId, couponId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof shareCoupon>>> = ({ signal }) =>
		shareCoupon(brandId, couponId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof shareCoupon>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type ShareCouponSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof shareCoupon>>>;
export type ShareCouponSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useShareCouponSuspense<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers: undefined | ShareCouponHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useShareCouponSuspense<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useShareCouponSuspense<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Share Coupon
 */

export function useShareCouponSuspense<
	TData = Awaited<ReturnType<typeof shareCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	couponId: string | undefined | null,
	headers?: ShareCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof shareCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getShareCouponSuspenseQueryOptions(brandId, couponId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get special accounts
 * @summary Get Special Accounts
 */
export const getSpecialAccounts = (
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustTerminalApiClientModelsSpecialAccountSpecialAccount[]>({
		url: `/external/incust/special_account`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetSpecialAccountsQueryKey = (params?: GetSpecialAccountsParams) => {
	return [`/external/incust/special_account`, ...(params ? [params] : [])] as const;
};

export const getGetSpecialAccountsQueryOptions = <
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetSpecialAccountsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getSpecialAccounts>>> = ({ signal }) =>
		getSpecialAccounts(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getSpecialAccounts>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetSpecialAccountsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getSpecialAccounts>>
>;
export type GetSpecialAccountsQueryError = ErrorType<HTTPValidationError>;

export function useGetSpecialAccounts<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetSpecialAccountsParams,
	headers: undefined | GetSpecialAccountsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getSpecialAccounts>>,
					TError,
					Awaited<ReturnType<typeof getSpecialAccounts>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccounts<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getSpecialAccounts>>,
					TError,
					Awaited<ReturnType<typeof getSpecialAccounts>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccounts<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Special Accounts
 */

export function useGetSpecialAccounts<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetSpecialAccountsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetSpecialAccountsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetSpecialAccountsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getSpecialAccounts>>> = ({ signal }) =>
		getSpecialAccounts(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getSpecialAccounts>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetSpecialAccountsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getSpecialAccounts>>
>;
export type GetSpecialAccountsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetSpecialAccountsSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetSpecialAccountsParams,
	headers: undefined | GetSpecialAccountsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Special Accounts
 */

export function useGetSpecialAccountsSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccounts>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsParams,
	headers?: GetSpecialAccountsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getSpecialAccounts>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetSpecialAccountsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get special accounts mapping
 * @summary Get Special Accounts Mapping
 */
export const getSpecialAccountsMapping = (
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GetSpecialAccountsMapping200>({
		url: `/external/incust/special_accounts_mapping`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetSpecialAccountsMappingQueryKey = (params?: GetSpecialAccountsMappingParams) => {
	return [`/external/incust/special_accounts_mapping`, ...(params ? [params] : [])] as const;
};

export const getGetSpecialAccountsMappingQueryOptions = <
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccountsMapping>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetSpecialAccountsMappingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getSpecialAccountsMapping>>> = ({
		signal,
	}) => getSpecialAccountsMapping(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetSpecialAccountsMappingQueryResult = NonNullable<
	Awaited<ReturnType<typeof getSpecialAccountsMapping>>
>;
export type GetSpecialAccountsMappingQueryError = ErrorType<HTTPValidationError>;

export function useGetSpecialAccountsMapping<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetSpecialAccountsMappingParams,
	headers: undefined | GetSpecialAccountsMappingHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccountsMapping>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
					TError,
					Awaited<ReturnType<typeof getSpecialAccountsMapping>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsMapping<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccountsMapping>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
					TError,
					Awaited<ReturnType<typeof getSpecialAccountsMapping>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsMapping<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccountsMapping>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Special Accounts Mapping
 */

export function useGetSpecialAccountsMapping<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getSpecialAccountsMapping>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetSpecialAccountsMappingQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetSpecialAccountsMappingSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetSpecialAccountsMappingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getSpecialAccountsMapping>>> = ({
		signal,
	}) => getSpecialAccountsMapping(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetSpecialAccountsMappingSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getSpecialAccountsMapping>>
>;
export type GetSpecialAccountsMappingSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetSpecialAccountsMappingSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetSpecialAccountsMappingParams,
	headers: undefined | GetSpecialAccountsMappingHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsMappingSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetSpecialAccountsMappingSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Special Accounts Mapping
 */

export function useGetSpecialAccountsMappingSuspense<
	TData = Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetSpecialAccountsMappingParams,
	headers?: GetSpecialAccountsMappingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getSpecialAccountsMapping>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetSpecialAccountsMappingSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get user wallet with ewallets
 * @summary Get User Wallet
 */
export const getUserWallet = (headers?: GetUserWalletHeaders, signal?: AbortSignal) => {
	return getAxios<IncustWalletsExt[]>({
		url: `/external/incust/wallet`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetUserWalletQueryKey = () => {
	return [`/external/incust/wallet`] as const;
};

export const getGetUserWalletQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserWalletQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserWallet>>> = ({ signal }) =>
		getUserWallet(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getUserWallet>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserWalletQueryResult = NonNullable<Awaited<ReturnType<typeof getUserWallet>>>;
export type GetUserWalletQueryError = ErrorType<HTTPValidationError>;

export function useGetUserWallet<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetUserWalletHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserWallet>>,
					TError,
					Awaited<ReturnType<typeof getUserWallet>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserWallet<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getUserWallet>>,
					TError,
					Awaited<ReturnType<typeof getUserWallet>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserWallet<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Wallet
 */

export function useGetUserWallet<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserWalletQueryOptions(headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetUserWalletSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUserWalletQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserWallet>>> = ({ signal }) =>
		getUserWallet(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getUserWallet>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserWalletSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUserWallet>>
>;
export type GetUserWalletSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetUserWalletSuspense<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetUserWalletHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserWalletSuspense<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetUserWalletSuspense<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get User Wallet
 */

export function useGetUserWalletSuspense<
	TData = Awaited<ReturnType<typeof getUserWallet>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetUserWalletHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetUserWalletSuspenseQueryOptions(headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust coupon info
 * @summary Process Coupon
 */
export const processCoupon = (
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustCouponExt>({
		url: `/external/incust/coupons/${couponIdOrCode}/process`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getProcessCouponQueryKey = (
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams
) => {
	return [
		`/external/incust/coupons/${couponIdOrCode}/process`,
		...(params ? [params] : []),
	] as const;
};

export const getProcessCouponQueryOptions = <
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getProcessCouponQueryKey(couponIdOrCode, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof processCoupon>>> = ({ signal }) =>
		processCoupon(couponIdOrCode, params, headers, signal);

	return { queryKey, queryFn, enabled: !!couponIdOrCode, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof processCoupon>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type ProcessCouponQueryResult = NonNullable<Awaited<ReturnType<typeof processCoupon>>>;
export type ProcessCouponQueryError = ErrorType<HTTPValidationError>;

export function useProcessCoupon<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params: undefined | ProcessCouponParams,
	headers: undefined | ProcessCouponHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof processCoupon>>,
					TError,
					Awaited<ReturnType<typeof processCoupon>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useProcessCoupon<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof processCoupon>>,
					TError,
					Awaited<ReturnType<typeof processCoupon>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useProcessCoupon<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Process Coupon
 */

export function useProcessCoupon<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getProcessCouponQueryOptions(couponIdOrCode, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getProcessCouponSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getProcessCouponQueryKey(couponIdOrCode, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof processCoupon>>> = ({ signal }) =>
		processCoupon(couponIdOrCode, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof processCoupon>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type ProcessCouponSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof processCoupon>>
>;
export type ProcessCouponSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useProcessCouponSuspense<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params: undefined | ProcessCouponParams,
	headers: undefined | ProcessCouponHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useProcessCouponSuspense<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useProcessCouponSuspense<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Process Coupon
 */

export function useProcessCouponSuspense<
	TData = Awaited<ReturnType<typeof processCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	params?: ProcessCouponParams,
	headers?: ProcessCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof processCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getProcessCouponSuspenseQueryOptions(
		couponIdOrCode,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust coupon info
 * @summary Check Coupon
 */
export const checkCoupon = (
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustCouponExt>({
		url: `/external/incust/coupons/${couponIdOrCode}/check`,
		method: "GET",
		headers,
		signal,
	});
};

export const getCheckCouponQueryKey = (couponIdOrCode: string | undefined | null) => {
	return [`/external/incust/coupons/${couponIdOrCode}/check`] as const;
};

export const getCheckCouponQueryOptions = <
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckCouponQueryKey(couponIdOrCode);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkCoupon>>> = ({ signal }) =>
		checkCoupon(couponIdOrCode, headers, signal);

	return { queryKey, queryFn, enabled: !!couponIdOrCode, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof checkCoupon>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckCouponQueryResult = NonNullable<Awaited<ReturnType<typeof checkCoupon>>>;
export type CheckCouponQueryError = ErrorType<HTTPValidationError>;

export function useCheckCoupon<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers: undefined | CheckCouponHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkCoupon>>,
					TError,
					Awaited<ReturnType<typeof checkCoupon>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckCoupon<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof checkCoupon>>,
					TError,
					Awaited<ReturnType<typeof checkCoupon>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckCoupon<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Coupon
 */

export function useCheckCoupon<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckCouponQueryOptions(couponIdOrCode, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getCheckCouponSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getCheckCouponQueryKey(couponIdOrCode);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof checkCoupon>>> = ({ signal }) =>
		checkCoupon(couponIdOrCode, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof checkCoupon>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CheckCouponSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof checkCoupon>>>;
export type CheckCouponSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useCheckCouponSuspense<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers: undefined | CheckCouponHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckCouponSuspense<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useCheckCouponSuspense<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Check Coupon
 */

export function useCheckCouponSuspense<
	TData = Awaited<ReturnType<typeof checkCoupon>>,
	TError = ErrorType<HTTPValidationError>,
>(
	couponIdOrCode: string | undefined | null,
	headers?: CheckCouponHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof checkCoupon>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getCheckCouponSuspenseQueryOptions(couponIdOrCode, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get incust gift card info by ID
 * @summary Get Gift Card By Id
 */
export const getGiftCardById = (
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GiftCard>({
		url: `/external/incust/gift_card/${giftCardId}`,
		method: "GET",
		headers,
		signal,
	});
};

export const getGetGiftCardByIdQueryKey = (giftCardId: string | undefined | null) => {
	return [`/external/incust/gift_card/${giftCardId}`] as const;
};

export const getGetGiftCardByIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetGiftCardByIdQueryKey(giftCardId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getGiftCardById>>> = ({ signal }) =>
		getGiftCardById(giftCardId, headers, signal);

	return { queryKey, queryFn, enabled: !!giftCardId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getGiftCardById>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetGiftCardByIdQueryResult = NonNullable<Awaited<ReturnType<typeof getGiftCardById>>>;
export type GetGiftCardByIdQueryError = ErrorType<HTTPValidationError>;

export function useGetGiftCardById<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers: undefined | GetGiftCardByIdHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getGiftCardById>>,
					TError,
					Awaited<ReturnType<typeof getGiftCardById>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetGiftCardById<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getGiftCardById>>,
					TError,
					Awaited<ReturnType<typeof getGiftCardById>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetGiftCardById<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Gift Card By Id
 */

export function useGetGiftCardById<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetGiftCardByIdQueryOptions(giftCardId, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetGiftCardByIdSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetGiftCardByIdQueryKey(giftCardId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getGiftCardById>>> = ({ signal }) =>
		getGiftCardById(giftCardId, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getGiftCardById>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetGiftCardByIdSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getGiftCardById>>
>;
export type GetGiftCardByIdSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetGiftCardByIdSuspense<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers: undefined | GetGiftCardByIdHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetGiftCardByIdSuspense<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetGiftCardByIdSuspense<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Gift Card By Id
 */

export function useGetGiftCardByIdSuspense<
	TData = Awaited<ReturnType<typeof getGiftCardById>>,
	TError = ErrorType<HTTPValidationError>,
>(
	giftCardId: string | undefined | null,
	headers?: GetGiftCardByIdHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getGiftCardById>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetGiftCardByIdSuspenseQueryOptions(giftCardId, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to add gift card to user
 * @summary Redeem Gift Card
 */
export const redeemGiftCard = (
	giftCardCode: string | undefined | null,
	headers?: RedeemGiftCardHeaders,
	signal?: AbortSignal
) => {
	return getAxios<CouponsAddedResponse>({
		url: `/external/incust/gift_card/${giftCardCode}`,
		method: "POST",
		headers,
		signal,
	});
};

export const getRedeemGiftCardMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemGiftCard>>,
		TError,
		{ giftCardCode: string | undefined | null; headers?: RedeemGiftCardHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof redeemGiftCard>>,
	TError,
	{ giftCardCode: string | undefined | null; headers?: RedeemGiftCardHeaders },
	TContext
> => {
	const mutationKey = ["redeemGiftCard"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof redeemGiftCard>>,
		{ giftCardCode: string | undefined | null; headers?: RedeemGiftCardHeaders }
	> = props => {
		const { giftCardCode, headers } = props ?? {};

		return redeemGiftCard(giftCardCode, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type RedeemGiftCardMutationResult = NonNullable<Awaited<ReturnType<typeof redeemGiftCard>>>;

export type RedeemGiftCardMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Redeem Gift Card
 */
export const useRedeemGiftCard = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof redeemGiftCard>>,
		TError,
		{ giftCardCode: string | undefined | null; headers?: RedeemGiftCardHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof redeemGiftCard>>,
	TError,
	{ giftCardCode: string | undefined | null; headers?: RedeemGiftCardHeaders },
	TContext
> => {
	const mutationOptions = getRedeemGiftCardMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Method to get incust coupons by ids
 * @summary Get Wlclient Coupons By Ids
 */
export const getWlclientCouponsByIds = (
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsCouponCoupon[]>({
		url: `/external/incust/${brandId}/wlclient/coupons`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetWlclientCouponsByIdsQueryKey = (
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams
) => {
	return [`/external/incust/${brandId}/wlclient/coupons`, ...(params ? [params] : [])] as const;
};

export const getGetWlclientCouponsByIdsQueryOptions = <
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getWlclientCouponsByIds>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetWlclientCouponsByIdsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getWlclientCouponsByIds>>> = ({
		signal,
	}) => getWlclientCouponsByIds(brandId, params, headers, signal);

	return { queryKey, queryFn, enabled: !!brandId, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetWlclientCouponsByIdsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getWlclientCouponsByIds>>
>;
export type GetWlclientCouponsByIdsQueryError = ErrorType<HTTPValidationError>;

export function useGetWlclientCouponsByIds<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers: undefined | GetWlclientCouponsByIdsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getWlclientCouponsByIds>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
					TError,
					Awaited<ReturnType<typeof getWlclientCouponsByIds>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetWlclientCouponsByIds<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getWlclientCouponsByIds>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
					TError,
					Awaited<ReturnType<typeof getWlclientCouponsByIds>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetWlclientCouponsByIds<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getWlclientCouponsByIds>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Wlclient Coupons By Ids
 */

export function useGetWlclientCouponsByIds<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getWlclientCouponsByIds>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetWlclientCouponsByIdsQueryOptions(brandId, params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetWlclientCouponsByIdsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetWlclientCouponsByIdsQueryKey(brandId, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getWlclientCouponsByIds>>> = ({
		signal,
	}) => getWlclientCouponsByIds(brandId, params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetWlclientCouponsByIdsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getWlclientCouponsByIds>>
>;
export type GetWlclientCouponsByIdsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetWlclientCouponsByIdsSuspense<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers: undefined | GetWlclientCouponsByIdsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetWlclientCouponsByIdsSuspense<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetWlclientCouponsByIdsSuspense<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Wlclient Coupons By Ids
 */

export function useGetWlclientCouponsByIdsSuspense<
	TData = Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
	TError = ErrorType<HTTPValidationError>,
>(
	brandId: number | undefined | null,
	params: GetWlclientCouponsByIdsParams,
	headers?: GetWlclientCouponsByIdsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getWlclientCouponsByIds>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetWlclientCouponsByIdsSuspenseQueryOptions(
		brandId,
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Incust All User Coupons
 */
export const getIncustAllUserCoupons = (
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<IncustClientApiClientModelsCouponCoupon[]>({
		url: `/external/incust/all_user_coupons`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustAllUserCouponsQueryKey = (params?: GetIncustAllUserCouponsParams) => {
	return [`/external/incust/all_user_coupons`, ...(params ? [params] : [])] as const;
};

export const getGetIncustAllUserCouponsQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustAllUserCoupons>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustAllUserCouponsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustAllUserCoupons>>> = ({
		signal,
	}) => getIncustAllUserCoupons(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustAllUserCouponsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustAllUserCoupons>>
>;
export type GetIncustAllUserCouponsQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustAllUserCoupons<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetIncustAllUserCouponsParams,
	headers: undefined | GetIncustAllUserCouponsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustAllUserCoupons>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
					TError,
					Awaited<ReturnType<typeof getIncustAllUserCoupons>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustAllUserCoupons<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustAllUserCoupons>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
					TError,
					Awaited<ReturnType<typeof getIncustAllUserCoupons>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustAllUserCoupons<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustAllUserCoupons>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust All User Coupons
 */

export function useGetIncustAllUserCoupons<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustAllUserCoupons>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustAllUserCouponsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustAllUserCouponsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustAllUserCouponsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustAllUserCoupons>>> = ({
		signal,
	}) => getIncustAllUserCoupons(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustAllUserCouponsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustAllUserCoupons>>
>;
export type GetIncustAllUserCouponsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustAllUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetIncustAllUserCouponsParams,
	headers: undefined | GetIncustAllUserCouponsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustAllUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustAllUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust All User Coupons
 */

export function useGetIncustAllUserCouponsSuspense<
	TData = Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetIncustAllUserCouponsParams,
	headers?: GetIncustAllUserCouponsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustAllUserCoupons>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustAllUserCouponsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * @summary Get Incust Settings By Context
 */
export const getIncustSettingsByContext = (
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	signal?: AbortSignal
) => {
	return getAxios<LoyaltySettingsSchema>({
		url: `/external/incust/settings/by_context`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getGetIncustSettingsByContextQueryKey = (params: GetIncustSettingsByContextParams) => {
	return [`/external/incust/settings/by_context`, ...(params ? [params] : [])] as const;
};

export const getGetIncustSettingsByContextQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettingsByContext>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustSettingsByContextQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustSettingsByContext>>> = ({
		signal,
	}) => getIncustSettingsByContext(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getIncustSettingsByContext>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustSettingsByContextQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustSettingsByContext>>
>;
export type GetIncustSettingsByContextQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustSettingsByContext<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers: undefined | GetIncustSettingsByContextHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettingsByContext>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustSettingsByContext>>,
					TError,
					Awaited<ReturnType<typeof getIncustSettingsByContext>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsByContext<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettingsByContext>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getIncustSettingsByContext>>,
					TError,
					Awaited<ReturnType<typeof getIncustSettingsByContext>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsByContext<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettingsByContext>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Settings By Context
 */

export function useGetIncustSettingsByContext<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getIncustSettingsByContext>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustSettingsByContextQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetIncustSettingsByContextSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustSettingsByContext>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetIncustSettingsByContextQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getIncustSettingsByContext>>> = ({
		signal,
	}) => getIncustSettingsByContext(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getIncustSettingsByContext>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetIncustSettingsByContextSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getIncustSettingsByContext>>
>;
export type GetIncustSettingsByContextSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetIncustSettingsByContextSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers: undefined | GetIncustSettingsByContextHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustSettingsByContext>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsByContextSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustSettingsByContext>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetIncustSettingsByContextSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustSettingsByContext>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Incust Settings By Context
 */

export function useGetIncustSettingsByContextSuspense<
	TData = Awaited<ReturnType<typeof getIncustSettingsByContext>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetIncustSettingsByContextParams,
	headers?: GetIncustSettingsByContextHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getIncustSettingsByContext>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetIncustSettingsByContextSuspenseQueryOptions(
		params,
		headers,
		options
	);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}
