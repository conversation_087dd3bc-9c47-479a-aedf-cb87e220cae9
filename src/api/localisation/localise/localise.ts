/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Localisation API
 * OpenAPI spec version: 0.1.0
 */
import { useMutation } from "@tanstack/react-query";
import type {
	MutationFunction,
	UseMutationOptions,
	UseMutationResult,
} from "@tanstack/react-query";
import type {
	HTTPValidationError,
	LocaliseDataSet,
	LocaliseDataSet200,
	LocaliseList200,
	LocaliseListData,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Localise Data Set
 */
export const localiseDataSet = (localiseDataSet: LocaliseDataSet, signal?: AbortSignal) => {
	return getAxios<LocaliseDataSet200>({
		url: `/localisation/dataSet`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: localiseDataSet,
		signal,
	});
};

export const getLocaliseDataSetMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof localiseDataSet>>,
		TError,
		{ data: LocaliseDataSet },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof localiseDataSet>>,
	TError,
	{ data: LocaliseDataSet },
	TContext
> => {
	const mutationKey = ["localiseDataSet"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof localiseDataSet>>,
		{ data: LocaliseDataSet }
	> = props => {
		const { data } = props ?? {};

		return localiseDataSet(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type LocaliseDataSetMutationResult = NonNullable<
	Awaited<ReturnType<typeof localiseDataSet>>
>;
export type LocaliseDataSetMutationBody = LocaliseDataSet;
export type LocaliseDataSetMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Localise Data Set
 */
export const useLocaliseDataSet = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof localiseDataSet>>,
		TError,
		{ data: LocaliseDataSet },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof localiseDataSet>>,
	TError,
	{ data: LocaliseDataSet },
	TContext
> => {
	const mutationOptions = getLocaliseDataSetMutationOptions(options);

	return useMutation(mutationOptions);
};
/**
 * Returns localisation list
 * @summary Localise List
 */
export const localiseList = (localiseListData: LocaliseListData, signal?: AbortSignal) => {
	return getAxios<LocaliseList200>({
		url: `/localisation/list`,
		method: "POST",
		headers: { "Content-Type": "application/json" },
		data: localiseListData,
		signal,
	});
};

export const getLocaliseListMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof localiseList>>,
		TError,
		{ data: LocaliseListData },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof localiseList>>,
	TError,
	{ data: LocaliseListData },
	TContext
> => {
	const mutationKey = ["localiseList"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof localiseList>>,
		{ data: LocaliseListData }
	> = props => {
		const { data } = props ?? {};

		return localiseList(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type LocaliseListMutationResult = NonNullable<Awaited<ReturnType<typeof localiseList>>>;
export type LocaliseListMutationBody = LocaliseListData;
export type LocaliseListMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Localise List
 */
export const useLocaliseList = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof localiseList>>,
		TError,
		{ data: LocaliseListData },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof localiseList>>,
	TError,
	{ data: LocaliseListData },
	TContext
> => {
	const mutationOptions = getLocaliseListMutationOptions(options);

	return useMutation(mutationOptions);
};
