/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Localisation API
 * OpenAPI spec version: 0.1.0
 */
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type { GetLocaleParams, GetLocaleResult, HTTPValidationError } from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * @summary Get Locale
 */
export const getLocale = (
	locale: string | undefined | null,
	params?: GetLocaleParams,
	signal?: AbortSignal
) => {
	return getAxios<GetLocaleResult>({
		url: `/localisation/locale/${locale}`,
		method: "GET",
		params,
		signal,
	});
};

export const getGetLocaleQueryKey = (
	locale: string | undefined | null,
	params?: GetLocaleParams
) => {
	return [`/localisation/locale/${locale}`, ...(params ? [params] : [])] as const;
};

export const getGetLocaleQueryOptions = <
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetLocaleQueryKey(locale, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getLocale>>> = ({ signal }) =>
		getLocale(locale, params, signal);

	return { queryKey, queryFn, enabled: !!locale, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getLocale>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetLocaleQueryResult = NonNullable<Awaited<ReturnType<typeof getLocale>>>;
export type GetLocaleQueryError = ErrorType<HTTPValidationError>;

export function useGetLocale<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params: undefined | GetLocaleParams,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getLocale>>,
					TError,
					Awaited<ReturnType<typeof getLocale>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLocale<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getLocale>>,
					TError,
					Awaited<ReturnType<typeof getLocale>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLocale<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Locale
 */

export function useGetLocale<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetLocaleQueryOptions(locale, params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetLocaleSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetLocaleQueryKey(locale, params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getLocale>>> = ({ signal }) =>
		getLocale(locale, params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getLocale>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetLocaleSuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getLocale>>>;
export type GetLocaleSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetLocaleSuspense<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params: undefined | GetLocaleParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLocaleSuspense<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetLocaleSuspense<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Locale
 */

export function useGetLocaleSuspense<
	TData = Awaited<ReturnType<typeof getLocale>>,
	TError = ErrorType<HTTPValidationError>,
>(
	locale: string | undefined | null,
	params?: GetLocaleParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getLocale>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetLocaleSuspenseQueryOptions(locale, params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}
