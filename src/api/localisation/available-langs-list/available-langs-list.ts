/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Localisation API
 * OpenAPI spec version: 0.1.0
 */
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseQueryResult,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	GetAvailableLanguagesParams,
	HTTPValidationError,
	LanguageSchema,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * Returns languages list
 * @summary Get Available Languages
 */
export const getAvailableLanguages = (
	params?: GetAvailableLanguagesParams,
	signal?: AbortSignal
) => {
	return getAxios<LanguageSchema[]>({
		url: `/localisation/available_languages`,
		method: "GET",
		params,
		signal,
	});
};

export const getGetAvailableLanguagesQueryKey = (params?: GetAvailableLanguagesParams) => {
	return [`/localisation/available_languages`, ...(params ? [params] : [])] as const;
};

export const getGetAvailableLanguagesQueryOptions = <
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailableLanguages>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAvailableLanguagesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAvailableLanguages>>> = ({
		signal,
	}) => getAvailableLanguages(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getAvailableLanguages>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAvailableLanguagesQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAvailableLanguages>>
>;
export type GetAvailableLanguagesQueryError = ErrorType<HTTPValidationError>;

export function useGetAvailableLanguages<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetAvailableLanguagesParams,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailableLanguages>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getAvailableLanguages>>,
					TError,
					Awaited<ReturnType<typeof getAvailableLanguages>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailableLanguages<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailableLanguages>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getAvailableLanguages>>,
					TError,
					Awaited<ReturnType<typeof getAvailableLanguages>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailableLanguages<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailableLanguages>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Available Languages
 */

export function useGetAvailableLanguages<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getAvailableLanguages>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetAvailableLanguagesQueryOptions(params, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetAvailableLanguagesSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getAvailableLanguages>>,
				TError,
				TData
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAvailableLanguagesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAvailableLanguages>>> = ({
		signal,
	}) => getAvailableLanguages(params, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getAvailableLanguages>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAvailableLanguagesSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAvailableLanguages>>
>;
export type GetAvailableLanguagesSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetAvailableLanguagesSuspense<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: undefined | GetAvailableLanguagesParams,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getAvailableLanguages>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailableLanguagesSuspense<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getAvailableLanguages>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAvailableLanguagesSuspense<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getAvailableLanguages>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Available Languages
 */

export function useGetAvailableLanguagesSuspense<
	TData = Awaited<ReturnType<typeof getAvailableLanguages>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params?: GetAvailableLanguagesParams,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<
				Awaited<ReturnType<typeof getAvailableLanguages>>,
				TError,
				TData
			>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetAvailableLanguagesSuspenseQueryOptions(params, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}
