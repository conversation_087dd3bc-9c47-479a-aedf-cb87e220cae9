/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Google Maps API
 * OpenAPI spec version: 0.1.0
 */
import {
	useInfiniteQuery,
	useMutation,
	useQuery,
	useSuspenseInfiniteQuery,
	useSuspenseQuery,
} from "@tanstack/react-query";
import type {
	DataTag,
	DefinedInitialDataOptions,
	DefinedUseInfiniteQueryResult,
	DefinedUseQueryResult,
	InfiniteData,
	MutationFunction,
	QueryFunction,
	QueryKey,
	UndefinedInitialDataOptions,
	UseInfiniteQueryOptions,
	UseInfiniteQueryResult,
	UseMutationOptions,
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	UseSuspenseInfiniteQueryOptions,
	UseSuspenseInfiniteQueryResult,
	UseSuspenseQueryOptions,
	UseSuspenseQueryResult,
} from "@tanstack/react-query";
import type {
	AddressComponent,
	AddressValidSchema,
	GeocodingResultSchema,
	GetMapsKeyHeaders,
	GetMapsPlaceDetailsHeaders,
	GetMapsPlaceDetailsParams,
	HTTPValidationError,
	MapsAutocompleteHeaders,
	MapsAutocompleteParams,
	MapsGeocodingHeaders,
	MapsGeocodingParams,
	MapsKeySchema,
	MapsReverseGeocodingHeaders,
	MapsReverseGeocodingParams,
	PlaceDetails,
	PredictionsResponse,
	ValidateAddressHeaders,
} from ".././schemas";
import { getAxios } from "../../instance";
import type { ErrorType } from "../../instance";

/**
 * Method to get autocomplete data for search query in google places
 * @summary Maps Autocomplete
 */
export const mapsAutocomplete = (
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	signal?: AbortSignal
) => {
	return getAxios<PredictionsResponse>({
		url: `/maps/autocomplete`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getMapsAutocompleteQueryKey = (params: MapsAutocompleteParams) => {
	return [`/maps/autocomplete`, ...(params ? [params] : [])] as const;
};

export const getMapsAutocompleteInfiniteQueryOptions = <
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsAutocompleteQueryKey(params);

	const queryFn: QueryFunction<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		QueryKey,
		MapsAutocompleteParams["offset"]
	> = ({ signal, pageParam }) =>
		mapsAutocomplete({ ...params, offset: pageParam || params?.["offset"] }, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		TError,
		TData,
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		QueryKey,
		MapsAutocompleteParams["offset"]
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsAutocompleteInfiniteQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsAutocomplete>>
>;
export type MapsAutocompleteInfiniteQueryError = ErrorType<HTTPValidationError>;

export function useMapsAutocompleteInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers: undefined | MapsAutocompleteHeaders,
	options: {
		query: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					TError,
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					QueryKey
				>,
				"initialData"
			>;
	}
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					TError,
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					QueryKey
				>,
				"initialData"
			>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Autocomplete
 */

export function useMapsAutocompleteInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsAutocompleteInfiniteQueryOptions(params, headers, options);

	const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getMapsAutocompleteQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsAutocompleteQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsAutocomplete>>> = ({ signal }) =>
		mapsAutocomplete(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsAutocompleteQueryResult = NonNullable<Awaited<ReturnType<typeof mapsAutocomplete>>>;
export type MapsAutocompleteQueryError = ErrorType<HTTPValidationError>;

export function useMapsAutocomplete<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers: undefined | MapsAutocompleteHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					TError,
					Awaited<ReturnType<typeof mapsAutocomplete>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocomplete<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsAutocomplete>>,
					TError,
					Awaited<ReturnType<typeof mapsAutocomplete>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocomplete<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Autocomplete
 */

export function useMapsAutocomplete<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsAutocompleteQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getMapsAutocompleteSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsAutocompleteQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsAutocomplete>>> = ({ signal }) =>
		mapsAutocomplete(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsAutocompleteSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsAutocomplete>>
>;
export type MapsAutocompleteSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useMapsAutocompleteSuspense<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers: undefined | MapsAutocompleteHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteSuspense<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteSuspense<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Autocomplete
 */

export function useMapsAutocompleteSuspense<
	TData = Awaited<ReturnType<typeof mapsAutocomplete>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsAutocomplete>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsAutocompleteSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getMapsAutocompleteSuspenseInfiniteQueryOptions = <
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsAutocompleteQueryKey(params);

	const queryFn: QueryFunction<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		QueryKey,
		MapsAutocompleteParams["offset"]
	> = ({ signal, pageParam }) =>
		mapsAutocomplete({ ...params, offset: pageParam || params?.["offset"] }, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseInfiniteQueryOptions<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		TError,
		TData,
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		QueryKey,
		MapsAutocompleteParams["offset"]
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsAutocompleteSuspenseInfiniteQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsAutocomplete>>
>;
export type MapsAutocompleteSuspenseInfiniteQueryError = ErrorType<HTTPValidationError>;

export function useMapsAutocompleteSuspenseInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers: undefined | MapsAutocompleteHeaders,
	options: {
		query: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteSuspenseInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsAutocompleteSuspenseInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Autocomplete
 */

export function useMapsAutocompleteSuspenseInfinite<
	TData = InfiniteData<
		Awaited<ReturnType<typeof mapsAutocomplete>>,
		MapsAutocompleteParams["offset"]
	>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsAutocompleteParams,
	headers?: MapsAutocompleteHeaders,
	options?: {
		query?: Partial<
			UseSuspenseInfiniteQueryOptions<
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				TError,
				TData,
				Awaited<ReturnType<typeof mapsAutocomplete>>,
				QueryKey,
				MapsAutocompleteParams["offset"]
			>
		>;
	}
): UseSuspenseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsAutocompleteSuspenseInfiniteQueryOptions(params, headers, options);

	const query = useSuspenseInfiniteQuery(queryOptions) as UseSuspenseInfiniteQueryResult<
		TData,
		TError
	> & { queryKey: DataTag<QueryKey, TData, TError> };

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get details for place by place_id
 * @summary Get Maps Place Details
 */
export const getMapsPlaceDetails = (
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	signal?: AbortSignal
) => {
	return getAxios<PlaceDetails>({ url: `/maps/details`, method: "GET", headers, params, signal });
};

export const getGetMapsPlaceDetailsQueryKey = (params: GetMapsPlaceDetailsParams) => {
	return [`/maps/details`, ...(params ? [params] : [])] as const;
};

export const getGetMapsPlaceDetailsQueryOptions = <
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMapsPlaceDetailsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMapsPlaceDetails>>> = ({ signal }) =>
		getMapsPlaceDetails(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getMapsPlaceDetails>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMapsPlaceDetailsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getMapsPlaceDetails>>
>;
export type GetMapsPlaceDetailsQueryError = ErrorType<HTTPValidationError>;

export function useGetMapsPlaceDetails<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers: undefined | GetMapsPlaceDetailsHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMapsPlaceDetails>>,
					TError,
					Awaited<ReturnType<typeof getMapsPlaceDetails>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsPlaceDetails<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMapsPlaceDetails>>,
					TError,
					Awaited<ReturnType<typeof getMapsPlaceDetails>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsPlaceDetails<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Maps Place Details
 */

export function useGetMapsPlaceDetails<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMapsPlaceDetailsQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetMapsPlaceDetailsSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMapsPlaceDetailsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMapsPlaceDetails>>> = ({ signal }) =>
		getMapsPlaceDetails(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getMapsPlaceDetails>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMapsPlaceDetailsSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof getMapsPlaceDetails>>
>;
export type GetMapsPlaceDetailsSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetMapsPlaceDetailsSuspense<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers: undefined | GetMapsPlaceDetailsHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsPlaceDetailsSuspense<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsPlaceDetailsSuspense<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Maps Place Details
 */

export function useGetMapsPlaceDetailsSuspense<
	TData = Awaited<ReturnType<typeof getMapsPlaceDetails>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: GetMapsPlaceDetailsParams,
	headers?: GetMapsPlaceDetailsHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsPlaceDetails>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMapsPlaceDetailsSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get addresses by coordinates
 * @summary Maps Reverse Geocoding
 */
export const mapsReverseGeocoding = (
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GeocodingResultSchema>({
		url: `/maps/reverse_geocoding`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getMapsReverseGeocodingQueryKey = (params: MapsReverseGeocodingParams) => {
	return [`/maps/reverse_geocoding`, ...(params ? [params] : [])] as const;
};

export const getMapsReverseGeocodingQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsReverseGeocodingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsReverseGeocoding>>> = ({ signal }) =>
		mapsReverseGeocoding(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof mapsReverseGeocoding>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsReverseGeocodingQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsReverseGeocoding>>
>;
export type MapsReverseGeocodingQueryError = ErrorType<HTTPValidationError>;

export function useMapsReverseGeocoding<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers: undefined | MapsReverseGeocodingHeaders,
	options: {
		query: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsReverseGeocoding>>,
					TError,
					Awaited<ReturnType<typeof mapsReverseGeocoding>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsReverseGeocoding<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsReverseGeocoding>>,
					TError,
					Awaited<ReturnType<typeof mapsReverseGeocoding>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsReverseGeocoding<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Reverse Geocoding
 */

export function useMapsReverseGeocoding<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsReverseGeocodingQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getMapsReverseGeocodingSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsReverseGeocodingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsReverseGeocoding>>> = ({ signal }) =>
		mapsReverseGeocoding(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof mapsReverseGeocoding>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsReverseGeocodingSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsReverseGeocoding>>
>;
export type MapsReverseGeocodingSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useMapsReverseGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers: undefined | MapsReverseGeocodingHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsReverseGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsReverseGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Reverse Geocoding
 */

export function useMapsReverseGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsReverseGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsReverseGeocodingParams,
	headers?: MapsReverseGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsReverseGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsReverseGeocodingSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get coordinates by addresses
 * @summary Maps Geocoding
 */
export const mapsGeocoding = (
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	signal?: AbortSignal
) => {
	return getAxios<GeocodingResultSchema>({
		url: `/maps/geocoding`,
		method: "GET",
		headers,
		params,
		signal,
	});
};

export const getMapsGeocodingQueryKey = (params: MapsGeocodingParams) => {
	return [`/maps/geocoding`, ...(params ? [params] : [])] as const;
};

export const getMapsGeocodingQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsGeocodingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsGeocoding>>> = ({ signal }) =>
		mapsGeocoding(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof mapsGeocoding>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsGeocodingQueryResult = NonNullable<Awaited<ReturnType<typeof mapsGeocoding>>>;
export type MapsGeocodingQueryError = ErrorType<HTTPValidationError>;

export function useMapsGeocoding<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers: undefined | MapsGeocodingHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsGeocoding>>,
					TError,
					Awaited<ReturnType<typeof mapsGeocoding>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsGeocoding<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof mapsGeocoding>>,
					TError,
					Awaited<ReturnType<typeof mapsGeocoding>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsGeocoding<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Geocoding
 */

export function useMapsGeocoding<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsGeocodingQueryOptions(params, headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getMapsGeocodingSuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getMapsGeocodingQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof mapsGeocoding>>> = ({ signal }) =>
		mapsGeocoding(params, headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof mapsGeocoding>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type MapsGeocodingSuspenseQueryResult = NonNullable<
	Awaited<ReturnType<typeof mapsGeocoding>>
>;
export type MapsGeocodingSuspenseQueryError = ErrorType<HTTPValidationError>;

export function useMapsGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers: undefined | MapsGeocodingHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useMapsGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Maps Geocoding
 */

export function useMapsGeocodingSuspense<
	TData = Awaited<ReturnType<typeof mapsGeocoding>>,
	TError = ErrorType<HTTPValidationError>,
>(
	params: MapsGeocodingParams,
	headers?: MapsGeocodingHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof mapsGeocoding>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getMapsGeocodingSuspenseQueryOptions(params, headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to get api key for Google Maps Javascript API
 * @summary Get Maps Key
 */
export const getMapsKey = (headers?: GetMapsKeyHeaders, signal?: AbortSignal) => {
	return getAxios<MapsKeySchema>({ url: `/maps/maps_key`, method: "GET", headers, signal });
};

export const getGetMapsKeyQueryKey = () => {
	return [`/maps/maps_key`] as const;
};

export const getGetMapsKeyQueryOptions = <
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMapsKeyQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMapsKey>>> = ({ signal }) =>
		getMapsKey(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
		Awaited<ReturnType<typeof getMapsKey>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMapsKeyQueryResult = NonNullable<Awaited<ReturnType<typeof getMapsKey>>>;
export type GetMapsKeyQueryError = ErrorType<HTTPValidationError>;

export function useGetMapsKey<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetMapsKeyHeaders,
	options: {
		query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>> &
			Pick<
				DefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMapsKey>>,
					TError,
					Awaited<ReturnType<typeof getMapsKey>>
				>,
				"initialData"
			>;
	}
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsKey<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>> &
			Pick<
				UndefinedInitialDataOptions<
					Awaited<ReturnType<typeof getMapsKey>>,
					TError,
					Awaited<ReturnType<typeof getMapsKey>>
				>,
				"initialData"
			>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsKey<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Maps Key
 */

export function useGetMapsKey<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>>;
	}
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMapsKeyQueryOptions(headers, options);

	const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

export const getGetMapsKeySuspenseQueryOptions = <
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetMapsKeyQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getMapsKey>>> = ({ signal }) =>
		getMapsKey(headers, signal);

	return { queryKey, queryFn, ...queryOptions } as UseSuspenseQueryOptions<
		Awaited<ReturnType<typeof getMapsKey>>,
		TError,
		TData
	> & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetMapsKeySuspenseQueryResult = NonNullable<Awaited<ReturnType<typeof getMapsKey>>>;
export type GetMapsKeySuspenseQueryError = ErrorType<HTTPValidationError>;

export function useGetMapsKeySuspense<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers: undefined | GetMapsKeyHeaders,
	options: {
		query: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsKeySuspense<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetMapsKeySuspense<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
/**
 * @summary Get Maps Key
 */

export function useGetMapsKeySuspense<
	TData = Awaited<ReturnType<typeof getMapsKey>>,
	TError = ErrorType<HTTPValidationError>,
>(
	headers?: GetMapsKeyHeaders,
	options?: {
		query?: Partial<
			UseSuspenseQueryOptions<Awaited<ReturnType<typeof getMapsKey>>, TError, TData>
		>;
	}
): UseSuspenseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
	const queryOptions = getGetMapsKeySuspenseQueryOptions(headers, options);

	const query = useSuspenseQuery(queryOptions) as UseSuspenseQueryResult<TData, TError> & {
		queryKey: DataTag<QueryKey, TData, TError>;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
}

/**
 * Method to validate address by google address_components
 * @summary Validate Address
 */
export const validateAddress = (
	addressComponent: AddressComponent[],
	headers?: ValidateAddressHeaders,
	signal?: AbortSignal
) => {
	return getAxios<AddressValidSchema>({
		url: `/maps/validate_address`,
		method: "POST",
		headers: { "Content-Type": "application/json", ...headers },
		data: addressComponent,
		signal,
	});
};

export const getValidateAddressMutationOptions = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof validateAddress>>,
		TError,
		{ data: AddressComponent[]; headers?: ValidateAddressHeaders },
		TContext
	>;
}): UseMutationOptions<
	Awaited<ReturnType<typeof validateAddress>>,
	TError,
	{ data: AddressComponent[]; headers?: ValidateAddressHeaders },
	TContext
> => {
	const mutationKey = ["validateAddress"];
	const { mutation: mutationOptions } = options
		? options.mutation && "mutationKey" in options.mutation && options.mutation.mutationKey
			? options
			: { ...options, mutation: { ...options.mutation, mutationKey } }
		: { mutation: { mutationKey } };

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof validateAddress>>,
		{ data: AddressComponent[]; headers?: ValidateAddressHeaders }
	> = props => {
		const { data, headers } = props ?? {};

		return validateAddress(data, headers);
	};

	return { mutationFn, ...mutationOptions };
};

export type ValidateAddressMutationResult = NonNullable<
	Awaited<ReturnType<typeof validateAddress>>
>;
export type ValidateAddressMutationBody = AddressComponent[];
export type ValidateAddressMutationError = ErrorType<HTTPValidationError>;

/**
 * @summary Validate Address
 */
export const useValidateAddress = <
	TError = ErrorType<HTTPValidationError>,
	TContext = unknown,
>(options?: {
	mutation?: UseMutationOptions<
		Awaited<ReturnType<typeof validateAddress>>,
		TError,
		{ data: AddressComponent[]; headers?: ValidateAddressHeaders },
		TContext
	>;
}): UseMutationResult<
	Awaited<ReturnType<typeof validateAddress>>,
	TError,
	{ data: AddressComponent[]; headers?: ValidateAddressHeaders },
	TContext
> => {
	const mutationOptions = getValidateAddressMutationOptions(options);

	return useMutation(mutationOptions);
};
