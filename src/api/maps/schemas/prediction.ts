/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Google Maps API
 * OpenAPI spec version: 0.1.0
 */
import type { MainTextMatchedSubstrings } from "./mainTextMatchedSubstrings";
import type { StructuredFormatting } from "./structuredFormatting";
import type { Terms } from "./terms";

export interface Prediction {
	description: string;
	matched_substrings?: MainTextMatchedSubstrings[];
	place_id: string;
	reference: string;
	structured_formatting?: StructuredFormatting;
	terms: Terms[];
	types: string[];
}
