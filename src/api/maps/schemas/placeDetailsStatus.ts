/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Google Maps API
 * OpenAPI spec version: 0.1.0
 */

export type PlaceDetailsStatus = (typeof PlaceDetailsStatus)[keyof typeof PlaceDetailsStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PlaceDetailsStatus = {
	OK: "OK",
	ZERO_RESULTS: "ZERO_RESULTS",
	NOT_FOUND: "NOT_FOUND",
	INVALID_REQUEST: "INVALID_REQUEST",
	OVER_QUERY_LIMIT: "OVER_QUERY_LIMIT",
	REQUEST_DENIED: "REQUEST_DENIED",
	UNKNOWN_ERROR: "UNKNOWN_ERROR",
} as const;
