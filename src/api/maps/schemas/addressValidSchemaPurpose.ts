/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Google Maps API
 * OpenAPI spec version: 0.1.0
 */

export type AddressValidSchemaPurpose =
	(typeof AddressValidSchemaPurpose)[keyof typeof AddressValidSchemaPurpose];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AddressValidSchemaPurpose = {
	street: "street",
	city: "city",
	country: "country",
	street_number: "street_number",
	default: "default",
} as const;
