/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * 7Loc Google Maps API
 * OpenAPI spec version: 0.1.0
 */
import type { AddressComponent } from "./addressComponent";
import type { GeocodingGeometry } from "./geocodingGeometry";

export interface GeocodingResultItemSchema {
	address_components: AddressComponent[];
	formatted_address: string;
	geometry: GeocodingGeometry;
	place_id: string;
	types: string[];
	location_type?: string;
}
